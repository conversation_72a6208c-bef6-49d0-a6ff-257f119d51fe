package com.devindie.keepsidian

import android.content.ContentUris
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.provider.CalendarContract
import android.util.Log
import androidx.core.net.toUri
import androidx.documentfile.provider.DocumentFile
import com.devindie.keepsidian.services.TodayNoteService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.withContext
import java.time.LocalDate
import java.time.format.DateTimeFormatter

data class TodoItem(
    val text: String,
    val isCompleted: Boolean,
    val originalLine: String,
    val indentLevel: Int = 0,
    val isCalendarEvent: Boolean = false,
    val calendarUri: String? = null
)

data class CalloutItem(
    val title: String,        // The title text (e.g., "OCR at 2:30 PM")
    val type: String,         // The type of callout (e.g., "OCR", "Voice")
    val time: String,         // The time string (e.g., "2:30 PM")
    val embeddedLink: String, // The embedded link path (e.g., "Clippings/OCR-2023-05-14-082210.md")
    val calloutType: String,        // The calloutType prefix (e.g., "🔍", "🎤")
    val originalBlock: String // The original callout block text
)

data class WidgetConfig(
    val vaultUriString: String,
    val dailyNotesFolder: String,
    val dailyNotesFormat: String = "yyyy-MM-dd"
) {
    fun isConfigured() = vaultUriString.isNotEmpty()
}


object TodoRepo {
    private const val TAG = "TodoRepo"

    private var _currentTodos = MutableStateFlow<List<TodoItem>>(emptyList())
    val currentTodos: StateFlow<List<TodoItem>> get() = _currentTodos

    private var _currentCallouts = MutableStateFlow<List<CalloutItem>>(emptyList())
    val currentCallouts: StateFlow<List<CalloutItem>> get() = _currentCallouts

    // Make _isLoading internal so it can be accessed by RefreshToDoAction
    internal var _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> get() = _isLoading

    // Regex pattern to validate calendar content URIs
    private val calendarContentUriPattern = Regex(
        "^content://com\\.android\\.calendar/time/\\d+$", RegexOption.IGNORE_CASE
    )

    // Regex patterns for parsing callout blocks
    // Match any callout type, not just info
    private val calloutHeaderPattern = Regex("> \\[!([\\w-]+)\\]- (.+)")

    // More flexible content pattern to match any content in the second line
    private val calloutContentPattern = Regex("> (.+?)!\\[\\[(.+?)\\]\\]")

    // Map of callout types to emoji/icon representations
    private val calloutTypeIcons = mapOf(
        "voice" to TodayNoteService.PREFIX_VOICE,
        "note" to TodayNoteService.PREFIX_NOTE,
        "voice note" to TodayNoteService.PREFIX_VOICE,
        "image" to TodayNoteService.PREFIX_IMAGE,
        "ocr scan" to TodayNoteService.PREFIX_OCR,
        // Default icon will be used for any type not in this map
    )

    /**
     * Gets an icon representation for a callout type
     *
     * @param type The callout type
     * @return An emoji/icon representing the callout type
     */
    private fun getCalloutTypeIcon(type: String): String {
        return calloutTypeIcons[type.lowercase()] ?: "📌" // Default icon for unknown types
    }

    /**
     * Checks if a URI string is a valid calendar content URI
     *
     * @param uriString The URI string to check
     * @return true if the URI is a valid calendar content URI, false otherwise
     */
    private fun isCalendarContentUri(uriString: String): Boolean {
        return calendarContentUriPattern.matches(uriString)
    }

    fun updateTodos(config: WidgetConfig, todos: List<TodoItem>) {
        if (!config.isConfigured()) return
        _currentTodos.value = todos
    }

    fun updateCallouts(config: WidgetConfig, callouts: List<CalloutItem>) {
        if (!config.isConfigured()) return
        _currentCallouts.value = callouts
    }

    /**
     * Handles a task click. For calendar events, returns true to indicate the click was handled
     * and the default toggle behavior should be skipped.
     *
     * @param context The context to use for starting activities
     * @param originalLine The original line of the task that was clicked
     * @return true if the click was handled (calendar event), false otherwise
     */
    fun handleTaskClick(context: Context, originalLine: String): Boolean {
        val todo = _currentTodos.value.find { it.originalLine == originalLine } ?: return false

        // If this is a calendar event with a valid URI, open it
        if (todo.isCalendarEvent && !todo.calendarUri.isNullOrEmpty()) {
            try {
                // Double-check that the URI is a valid calendar URI
                if (!isCalendarContentUri(todo.calendarUri)) {
                    Log.d(TAG, "Invalid calendar URI format: ${todo.calendarUri}")
                    return false
                }

                // Parse the URI and create the intent with the correct format
                val uri = Uri.parse(todo.calendarUri)
                val builder: Uri.Builder =
                    CalendarContract.CONTENT_URI.buildUpon().appendPath("time")

                // Extract the timestamp from the URI's last path segment
                val timestamp = uri.lastPathSegment?.toLong() ?: 0L
                if (timestamp <= 0) {
                    Log.d(TAG, "Invalid timestamp in calendar URI: $timestamp")
                    return false
                }

                // Append the timestamp as an ID
                ContentUris.appendId(builder, timestamp)

                // Create and start the intent
                val intent = Intent(Intent.ACTION_VIEW).setData(builder.build())
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(intent)
                return true
            } catch (e: Exception) {
                e.printStackTrace()
                // If there's an error, fall back to the default toggle behavior
                return false
            }
        }

        // Not a calendar event, let the default toggle behavior handle it
        return false
    }

    /**
     * Handles a callout click by opening the embedded link in Obsidian
     *
     * @param context The context to use for starting activities
     * @param originalBlock The original block text of the callout that was clicked
     * @return true if the click was handled, false otherwise
     */
    fun handleCalloutClick(context: Context, originalBlock: String): Boolean {
        val callout =
            _currentCallouts.value.find { it.originalBlock == originalBlock } ?: return false

        try {
            // Get the vault name from preferences
            val appDataStore = AppModule.provideAppDataStoreRepository(context.applicationContext)
            val vaultUriString =
                appDataStore.getStringValue(Constants.Preferences.OBSIDIAN_VAULT_URI, "")

            if (vaultUriString.isEmpty()) {
                Log.e(TAG, "Vault URI not configured")
                return false
            }

            // Get vault name from URI
            val vaultDoc = DocumentFile.fromTreeUri(context, Uri.parse(vaultUriString))
            val vaultName = vaultDoc?.name ?: "vault"

            // Clean up the embedded link path - it comes from ![[path]] format
            // We need to remove any brackets and ensure it's properly formatted
            val cleanPath = callout.embeddedLink.trim()

            // Log detailed information for debugging
            Log.d(TAG, "Callout details:")
            Log.d(TAG, "- Title: ${callout.title}")
            Log.d(TAG, "- Type: ${callout.type}")
            Log.d(TAG, "- Embedded link: ${callout.embeddedLink}")
            Log.d(TAG, "- Vault name: $vaultName")
            Log.d(TAG, "- Original block: ${callout.originalBlock}")

            // Instead of directly creating an intent with the obsidian URI,
            // use OpenObsidianActivity to handle opening the link
            val intent = Intent(context, OpenObsidianActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)

            // Pass the vault name and file path as extras
            intent.putExtra(OpenObsidianActivity.EXTRA_VAULT_NAME, vaultName)
            intent.putExtra(OpenObsidianActivity.EXTRA_FILE_PATH, cleanPath)

            // Log the intent details for debugging
            Log.d(TAG, "Starting OpenObsidianActivity with vault: $vaultName, file: $cleanPath")

            // Start the activity
            context.startActivity(intent)
            return true
        } catch (e: Exception) {
            Log.e(TAG, "Error opening callout link in Obsidian", e)
            e.printStackTrace()
            return false
        }
    }

    fun toggleTodoState(originalLine: String) {
        val currentList = _currentTodos.value
        val updatedList = currentList.map { todo ->
            if (todo.originalLine == originalLine) {
                todo.copy(isCompleted = !todo.isCompleted)
            } else {
                todo
            }
        }
        _currentTodos.value = updatedList
    }

    /**
     * Parses callout blocks from the content of a markdown file
     * Looks for blocks in the format:
     * > [!any-type]- {Any title text}
     * > {Any prefix content}![[relativePath]]
     */
    private fun parseCalloutsFromContent(content: String): List<CalloutItem> {
        val calloutItems = mutableListOf<CalloutItem>()
        val lines = content.lines()

        var i = 0
        while (i < lines.size - 1) { // Need at least 2 lines for a callout block
            val currentLine = lines[i]
            val nextLine = lines[i + 1]

            // Check if current line matches callout header pattern
            val headerMatch = calloutHeaderPattern.matchEntire(currentLine)
            if (headerMatch != null) {
                // Extract callout type and title
                val calloutType = headerMatch.groupValues[1].trim()
                val fullTitle = headerMatch.groupValues[2].trim()
                Log.e("fullTitle:", fullTitle.toString())
                // Try to extract type and time if the title follows the "{Type} at {time}" format
                val titleParts = fullTitle.split(" at ", limit = 2)
                val type = titleParts.getOrNull(0) ?: fullTitle
                val time = titleParts.getOrNull(1) ?: ""
                Log.e("type:", type.toString())
                // Check if next line matches callout content pattern
                val contentMatch = calloutContentPattern.matchEntire(nextLine)
                if (contentMatch != null) {
                    // Extract prefix (which may include emoji) and embedded link
                    contentMatch.groupValues[1].trim()
                    val embeddedLink = contentMatch.groupValues[2]

                    // Create callout item
                    calloutItems.add(
                        CalloutItem(
                            title = fullTitle,
                            type = type,
                            time = time,
                            embeddedLink = embeddedLink,
                            calloutType = getCalloutTypeIcon(type),
                            originalBlock = "$currentLine\n$nextLine"
                        )
                    )

                    // Skip the next line since we've processed it
                    i += 2
                    continue
                }
            }

            // If not a callout block, move to next line
            i++
        }

        return calloutItems
    }

    suspend fun loadTodosFromFile(context: Context, config: WidgetConfig): List<TodoItem> {
        if (!config.isConfigured()) return emptyList()

        // Set loading state to true
        _isLoading.value = true

        return withContext(Dispatchers.IO) {
            try {
                val vaultUri = config.vaultUriString.toUri()
                val vaultDoc =
                    DocumentFile.fromTreeUri(context, vaultUri) ?: return@withContext emptyList()

                // Navigate to daily notes folder
                val dailyNotesDir = if (config.dailyNotesFolder.isEmpty()) {
                    vaultDoc
                } else {
                    val folders = config.dailyNotesFolder.split("/")
                    var currentDir = vaultDoc

                    for (folder in folders) {
                        if (folder.isEmpty()) continue
                        currentDir = currentDir.findFile(folder) ?: return@withContext emptyList()
                    }
                    currentDir
                }

                // Get today's date using the configured format
                val today = LocalDate.now()
                val formatter = DateTimeFormatter.ofPattern(config.dailyNotesFormat)
                val todayFileName = "${today.format(formatter)}.md"

                // Find today's note file
                val todayFile = dailyNotesDir.findFile(todayFileName)

                if (todayFile != null && todayFile.canRead()) {
                    // Read the file content
                    val inputStream = context.contentResolver.openInputStream(todayFile.uri)
                    val content = inputStream?.bufferedReader()?.use { it.readText() } ?: ""
                    inputStream?.close()

                    // Convert raw task strings to TodoItems
                    val todoItems = content.lines()
                        .filter { it.trim().startsWith("- [ ]") || it.trim().startsWith("- [x]") }
                        .map { line ->
                            val isCompleted = line.trim().startsWith("- [x]")
                            val taskText = line.substringAfter("]").trim()
                            val indentLevel =
                                line.takeWhile { it.isWhitespace() }.count { it == '\t' }

                            // Extract potential calendar URI if present in markdown link format
                            val potentialUri =
                                if (taskText.contains("](") && taskText.contains(")")) {
                                    taskText.substringAfter("](").substringBefore(")")
                                } else {
                                    null
                                }

                            // Check if this is a calendar event task by:
                            // 1. Has the calendar icon prefix
                            // 2. Contains a valid calendar content URI
                            val isCalendarEvent =
                                taskText.startsWith(TodayNoteService.CALENDAR_EVENT_PREFIX) && potentialUri != null && isCalendarContentUri(
                                    potentialUri
                                )

                            // Use the URI only if it's a valid calendar URI
                            val calendarUri = if (isCalendarEvent) potentialUri else null

                            TodoItem(
                                text = taskText,
                                isCompleted = isCompleted,
                                originalLine = line.trim(),
                                indentLevel = indentLevel,
                                isCalendarEvent = isCalendarEvent,
                                calendarUri = calendarUri
                            )
                        }

                    // Parse callout blocks
                    val calloutItems = parseCalloutsFromContent(content)

                    // Update repositories
                    updateTodos(config, todoItems)
                    updateCallouts(config, calloutItems)

                    // Set loading state to false
                    _isLoading.value = false
                    return@withContext todoItems
                }

                // Set loading state to false
                _isLoading.value = false
                emptyList()
            } catch (e: Exception) {
                e.printStackTrace()
                // Set loading state to false
                _isLoading.value = false
                emptyList()
            }
        }
    }
}

