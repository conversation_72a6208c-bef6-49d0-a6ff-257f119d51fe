package com.devindie.keepsidian

import android.widget.Toast
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.devindie.keepsidian.analytics.AnalyticsHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun OfflineSpeechRecognitionToggle(
    onEnableOfflineRecognition: (Boolean) -> Unit,
    isEnabled: Boolean = true,
    autoEnable: Boolean = false
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val appDataStore = AppModule.provideAppDataStoreRepository(context)

    // Get the current preference value or default to false (disabled)
    var enableOfflineRecognition by remember {
        mutableStateOf(
            if (autoEnable) true
            else appDataStore.getBooleanValue(Constants.Preferences.ENABLE_OFFLINE_SPEECH_RECOGNITION, false)
        )
    }

    // Track download progress
    var downloadProgress by remember { mutableStateOf(0) }
    var isDownloading by remember { mutableStateOf(false) }

    // Create a download manager instance
    val modelDownloadManager = remember { ModelDownloadManager(context) }

    // Register with lifecycle if context is a lifecycle owner
    LaunchedEffect(Unit) {
        if (context is androidx.lifecycle.LifecycleOwner) {
            context.lifecycle.addObserver(modelDownloadManager)
        }
    }

    // If autoEnable is true, update the preference value
    LaunchedEffect(autoEnable) {
        if (autoEnable && isEnabled) {
            coroutineScope.launch(Dispatchers.IO) {
                appDataStore.putBoolean(Constants.Preferences.ENABLE_OFFLINE_SPEECH_RECOGNITION, true)
            }
            onEnableOfflineRecognition(true)
            // Track preference change for auto-enable
            AnalyticsHelper.trackPreferenceChange(Constants.Preferences.ENABLE_OFFLINE_SPEECH_RECOGNITION, "true")
        }
    }

    Card(
        modifier = Modifier
            .fillMaxWidth(0.9f)
            .padding(vertical = 8.dp)
            .alpha(if (isEnabled) 1f else 0.6f),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        ),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = stringResource(R.string.offline_speech_recognition),
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = stringResource(R.string.offline_speech_recognition_description),
                style = MaterialTheme.typography.bodyMedium
            )

            Spacer(modifier = Modifier.height(16.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = if (enableOfflineRecognition)
                        stringResource(R.string.offline_speech_recognition_enabled)
                    else
                        stringResource(R.string.offline_speech_recognition_disabled),
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )

                Switch(
                    checked = enableOfflineRecognition,
                    onCheckedChange = { enabled ->
                        if (isEnabled) {
                            enableOfflineRecognition = enabled
                            coroutineScope.launch(Dispatchers.IO) {
                                appDataStore.putBoolean(Constants.Preferences.ENABLE_OFFLINE_SPEECH_RECOGNITION, enabled)
                            }
                            onEnableOfflineRecognition(enabled)
                            // Track preference change
                            AnalyticsHelper.trackPreferenceChange(Constants.Preferences.ENABLE_OFFLINE_SPEECH_RECOGNITION, enabled.toString())

                            // Show toast message and start model download if enabled
                            if (enabled) {
                                Toast.makeText(
                                    context,
                                    context.getString(R.string.offline_speech_recognition_enabled),
                                    Toast.LENGTH_SHORT
                                ).show()

                                // Start the download if not already downloading
                                if (!isDownloading) {
                                    isDownloading = true

                                    modelDownloadManager.downloadModelIfNeeded(
                                        callback = { success ->
                                            isDownloading = false
                                            if (success) {
                                                Toast.makeText(
                                                    context,
                                                    context.getString(R.string.offline_transcription_complete),
                                                    Toast.LENGTH_SHORT
                                                ).show()
                                            } else {
                                                // If download failed, show error message
                                                Toast.makeText(
                                                    context,
                                                    context.getString(R.string.speech_model_download_failed),
                                                    Toast.LENGTH_SHORT
                                                ).show()
                                            }
                                        },
                                        progressUpdate = { progress ->
                                            downloadProgress = progress
                                        }
                                    )
                                }
                            }
                        }
                    },
                    enabled = isEnabled
                )
            }

            // Show download progress if downloading
            if (isDownloading) {
                Spacer(modifier = Modifier.height(8.dp))

                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f),
                            RoundedCornerShape(4.dp)
                        )
                        .padding(8.dp)
                ) {
                    Text(
                        text = "Downloading speech recognition model: $downloadProgress%",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.primary
                    )

                    Spacer(modifier = Modifier.height(4.dp))

                    LinearProgressIndicator(
                        progress = { downloadProgress / 100f },
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }

            // Show warning if speech-to-text is not enabled
            val isSpeechToTextEnabled = appDataStore.getBooleanValue(Constants.Preferences.ENABLE_SPEECH_TO_TEXT, false)
            if (!isSpeechToTextEnabled && enableOfflineRecognition) {
                Spacer(modifier = Modifier.height(8.dp))

                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            MaterialTheme.colorScheme.errorContainer.copy(alpha = 0.3f),
                            RoundedCornerShape(4.dp)
                        )
                        .padding(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Speech-To-Text must be enabled for offline recognition to work.",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.error
                    )
                }
            } else if (!isEnabled) {
                Spacer(modifier = Modifier.height(8.dp))

                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f),
                            RoundedCornerShape(4.dp)
                        )
                        .padding(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = stringResource(R.string.select_vault_to_enable),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
            }
        }
    }
}
