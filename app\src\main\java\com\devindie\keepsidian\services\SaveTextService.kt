package com.devindie.keepsidian.services

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.util.Log
import androidx.core.net.toUri
import androidx.documentfile.provider.DocumentFile
import com.devindie.keepsidian.utils.WidgetUtils
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class SaveTextService : BaseProcessingService() {
    companion object {
        private const val TAG = "SaveTextService"
        private const val NOTIFICATION_ID = 1001

        const val EXTRA_TEXT = "com.devindie.keepsidian.extra.TEXT"
        const val EXTRA_VAULT_URI = "com.devindie.keepsidian.extra.VAULT_URI"

        fun startService(context: Context, text: String, vaultUri: String) {
            val intent = Intent(context, SaveTextService::class.java).apply {
                putExtra(EXTRA_TEXT, text)
                putExtra(EXTRA_VAULT_URI, vaultUri)
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }
    }

    override val serviceName: String = TAG
    override val notificationId: Int = NOTIFICATION_ID

    override fun onStartCommand(intent: Intent, flags: Int, startId: Int): Int {
        val text = intent.getStringExtra(EXTRA_TEXT) ?: ""
        val vaultUriString = intent.getStringExtra(EXTRA_VAULT_URI) ?: ""

        if (text.isEmpty() || vaultUriString.isEmpty()) {
            stopSelf(startId)
            return super.onStartCommand(intent, flags, startId)
        }

        // Start as foreground service
        startForeground(notificationId, createNotification(
            "Saving Text",
            "Processing and saving to Obsidian..."
        ))

        // Process in background
        serviceScope.launch {
            try {
                // Format the text as markdown
                val markdownContent = formatAsMarkdown(text)

                // Save to Obsidian vault
                val fileUri = saveToObsidianVault(markdownContent, vaultUriString)

                if (fileUri != null) {
                    // Success - update notification
                    updateNotification("Text Saved", "Successfully saved to Obsidian")

                    // Add link to today's note
                    addLinkToTodayNote(fileUri, vaultUriString)

                    // Refresh widgets
                    WidgetUtils.refreshDailyTodoWidgets(this@SaveTextService)
                } else {
                    // Failed to save
                    updateNotification("Save Failed", "Could not save text to Obsidian")
                }

                // Stop service after a short delay
                Thread.sleep(1000)
                stopSelf(startId)
            } catch (e: Exception) {
                Log.e(TAG, "Error saving text", e)
                updateNotification("Error", "Failed to save text: ${e.message}")
                stopSelf(startId)
            }
        }

        // Use the parent implementation which returns START_REDELIVER_INTENT
        return super.onStartCommand(intent, flags, startId)
    }

    private fun formatAsMarkdown(text: String): String {
        val sb = StringBuilder()
        val currentDate = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault()).format(Date())

        // Create title from first few words or first line
        val title = if (text.contains("\n")) {
            text.substringBefore("\n").take(50).trim()
        } else {
            text.take(50).trim()
        }

        // Add YAML frontmatter
        sb.append("---\n")
        sb.append("title: $title\n")
        sb.append("date: $currentDate\n")
        sb.append("---\n\n")

        // Add content
        sb.append(text)

        return sb.toString()
    }

    private fun saveToObsidianVault(content: String, vaultUriString: String): Uri? {
        try {
            val vaultUri = vaultUriString.toUri()
            val vaultDoc = DocumentFile.fromTreeUri(this, vaultUri) ?: return null

            // Create a filename based on current date/time
            val timestamp = SimpleDateFormat("yyyyMMdd-HHmmss", Locale.US).format(Date())
            val fileName = "note-$timestamp.md"

            // Create the file in the vault
            val newFile = vaultDoc.createFile("text/markdown", fileName) ?: return null

            // Write the content to the file
            contentResolver.openOutputStream(newFile.uri)?.use { outputStream ->
                outputStream.write(content.toByteArray())
            }

            return newFile.uri
        } catch (e: Exception) {
            Log.e(TAG, "Error saving to vault", e)
            return null
        }
    }

    private fun addLinkToTodayNote(fileUri: Uri, vaultUriString: String) {
        // Extract a title from the file URI
        val fileName = fileUri.lastPathSegment?.substringAfterLast('/') ?: "Note"
        val title = fileName.substringBeforeLast('.').replace("-", " ")

        // Start the TodayNoteService to add the link
        TodayNoteService.startLinkService(
            context = this,
            fileUri = fileUri.toString(),
            fileTitle = title,
            vaultUri = vaultUriString,
            fileType = "note"
        )
    }
}
