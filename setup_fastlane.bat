@echo off
REM VaultSnap Fastlane Setup Script for Windows
REM This script helps set up Fastlane for the VaultSnap Android project

echo 🚀 Setting up Fastlane for VaultSnap...

REM Check if Ruby is installed
ruby --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Ruby is not installed. Please install Ruby first.
    echo    Visit: https://rubyinstaller.org/
    pause
    exit /b 1
)

echo ✅ Ruby found
ruby --version

REM Check if <PERSON><PERSON><PERSON> is installed
bundle --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 Installing Bundler...
    gem install bundler
) else (
    echo ✅ Bundler found
    bundle --version
)

REM Install Ruby dependencies
echo 📦 Installing Ruby dependencies...
bundle install

REM Install Fastlane plugins
echo 🔌 Installing Fastlane plugins...
bundle exec fastlane install_plugins

REM Create .env.local from template
if not exist "fastlane\.env.local" (
    echo 📝 Creating .env.local from template...
    copy "fastlane\.env" "fastlane\.env.local"
    echo ⚠️  Please edit fastlane\.env.local with your actual credentials
) else (
    echo ✅ .env.local already exists
)

REM Create local.properties from template if it doesn't exist
if not exist "local.properties" (
    echo 📝 Creating local.properties from template...
    copy "local.properties.template" "local.properties"
    echo ⚠️  Please edit local.properties with your SDK path and signing configuration
) else (
    echo ✅ local.properties already exists
)

echo.
echo 🎉 Fastlane setup complete!
echo.
echo Next steps:
echo 1. Edit fastlane\.env.local with your Firebase and Google Play credentials
echo 2. Edit local.properties with your signing configuration
echo 3. Create a keystore for release signing
echo 4. Test the setup with: bundle exec fastlane build_debug
echo.
echo Available commands:
echo   bundle exec fastlane test           - Run tests
echo   bundle exec fastlane build_debug    - Build debug APK
echo   bundle exec fastlane build_release  - Build release APK
echo   bundle exec fastlane deploy_firebase - Deploy to Firebase
echo.
echo For more information, see fastlane\README.md
pause
