#!/bin/bash

# <PERSON>ript to create a release keystore for VaultSnap

echo "🔐 Creating release keystore for VaultSnap..."
echo

# Create signing directory if it doesn't exist
mkdir -p signing

echo "This will create a keystore file for signing your release APKs."
echo "Please provide the following information:"
echo

read -s -p "Enter keystore password (remember this!): " STORE_PASSWORD
echo
read -s -p "Enter key password (can be same as keystore password): " KEY_PASSWORD
echo
read -p "Enter key alias (e.g., vaultsnap): " ALIAS_NAME
read -p "Enter your name: " YOUR_NAME
read -p "Enter organizational unit (e.g., Development): " ORG_UNIT
read -p "Enter organization name: " ORGANIZATION
read -p "Enter city: " CITY
read -p "Enter state/province: " STATE
read -p "Enter country code (e.g., US): " COUNTRY

echo
echo "Creating keystore with the following details:"
echo "- Keystore: signing/vaultsnap-release.jks"
echo "- Alias: $ALIAS_NAME"
echo "- Validity: 25 years"
echo

keytool -genkey -v \
    -keystore signing/vaultsnap-release.jks \
    -keyalg RSA \
    -keysize 2048 \
    -validity 9125 \
    -alias "$ALIAS_NAME" \
    -storepass "$STORE_PASSWORD" \
    -keypass "$KEY_PASSWORD" \
    -dname "CN=$YOUR_NAME, OU=$ORG_UNIT, O=$ORGANIZATION, L=$CITY, ST=$STATE, C=$COUNTRY"

if [ $? -eq 0 ]; then
    echo
    echo "✅ Keystore created successfully!"
    echo
    echo "📝 Add these lines to your local.properties file:"
    echo "KEYSTORE_PATH=signing/vaultsnap-release.jks"
    echo "KEYSTORE_PASSWORD=$STORE_PASSWORD"
    echo "KEY_ALIAS=$ALIAS_NAME"
    echo "KEY_PASSWORD=$KEY_PASSWORD"
    echo
    echo "⚠️  IMPORTANT: Keep these credentials safe and never commit them to version control!"
    echo
    read -p "Would you like me to add these to local.properties automatically? (y/n): " ADD_TO_LOCAL
    
    if [[ "$ADD_TO_LOCAL" =~ ^[Yy]$ ]]; then
        echo "" >> local.properties
        echo "# Signing configuration for release builds" >> local.properties
        echo "KEYSTORE_PATH=signing/vaultsnap-release.jks" >> local.properties
        echo "KEYSTORE_PASSWORD=$STORE_PASSWORD" >> local.properties
        echo "KEY_ALIAS=$ALIAS_NAME" >> local.properties
        echo "KEY_PASSWORD=$KEY_PASSWORD" >> local.properties
        echo
        echo "✅ Configuration added to local.properties"
    fi
else
    echo
    echo "❌ Failed to create keystore. Please check the error messages above."
fi

echo
echo "Press Enter to exit..."
read
