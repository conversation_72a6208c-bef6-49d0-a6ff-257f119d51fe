# Environment variables for Fastlane
# Copy this file to .env.local and fill in your actual values
# .env.local is ignored by git for security

# Firebase App Distribution
# FIREBASE_APP_ID=your-firebase-app-id-here
# FIREBASE_TOKEN=1//0g_I3rdFddmmhCgYIARAAGBASNwF-L9IrYHCS31jqmqWrpeQYyPXf_k73m2Vs_nDkpiNn2MQ9Si2XDKzXZuWJwUjpliv-VzIUjUE

# Google Play Console
# GOOGLE_PLAY_JSON_KEY_PATH=path/to/your/google-play-console-key.json

# Signing
# KEYSTORE_PATH=path/to/your/keystore.jks
# KEYSTORE_PASSWORD=your-keystore-password
# KEY_ALIAS=your-key-alias
# KEY_PASSWORD=your-key-password

# Slack notifications (optional)
# SLACK_URL=your-slack-webhook-url

# Other environment variables
# GRADLE_OPTS=-Xmx4g -XX:MaxPermSize=512m
