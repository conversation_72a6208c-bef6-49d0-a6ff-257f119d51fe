package com.devindie.keepsidian.services

import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.core.net.toUri
import androidx.documentfile.provider.DocumentFile
import com.devindie.keepsidian.R
import com.devindie.keepsidian.YouTubeMetadata
import com.devindie.keepsidian.YouTubeService
import com.devindie.keepsidian.analytics.AnalyticsHelper
import io.ktor.client.HttpClient
import io.ktor.client.engine.android.Android
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class YouTubeMetadataService : BaseProcessingService() {
    companion object {
        private const val TAG = "YouTubeMetadataService"
        private const val NOTIFICATION_ID = 1003

        const val EXTRA_VIDEO_ID = "com.devindie.keepsidian.extra.VIDEO_ID"
        const val EXTRA_TEXT = "com.devindie.keepsidian.extra.TEXT"
        const val EXTRA_VAULT_URI = "com.devindie.keepsidian.extra.VAULT_URI"

        fun startService(context: Context, videoId: String, text: String, vaultUri: String) {
            val intent = Intent(context, YouTubeMetadataService::class.java).apply {
                putExtra(EXTRA_VIDEO_ID, videoId)
                putExtra(EXTRA_TEXT, text)
                putExtra(EXTRA_VAULT_URI, vaultUri)
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }
    }

    override val serviceName: String = TAG
    override val notificationId: Int = NOTIFICATION_ID

    private val youTubeService by lazy { YouTubeService(HttpClient(Android), this) }

    override fun onStartCommand(intent: Intent, flags: Int, startId: Int): Int {
        val videoId = intent?.getStringExtra(EXTRA_VIDEO_ID) ?: ""
        val sharedText = intent?.getStringExtra(EXTRA_TEXT) ?: ""
        val vaultUriString = intent?.getStringExtra(EXTRA_VAULT_URI) ?: ""

        if (videoId.isEmpty() || vaultUriString.isEmpty()) {
            stopSelf(startId)
            return super.onStartCommand(intent, flags, startId)
        }

        // Start as foreground service
        startForeground(notificationId, createNotification(
            "Processing YouTube Link",
            "Fetching metadata..."
        ))

        // Process in background
        serviceScope.launch {
            try {
                // Fetch YouTube metadata
                updateNotification("Processing YouTube Link", "Fetching video metadata...")
                val metadataResult = youTubeService.getYouTubeMetadata(videoId)

                if (metadataResult.isSuccess) {
                    val metadata = metadataResult.getOrNull()!!
                    updateNotification("Processing YouTube Link", "Creating markdown file...")

                    // Format the metadata as markdown
                    val markdownContent = formatAsMarkdown(metadata, videoId, sharedText)

                    // Save to Obsidian vault
                    val fileUri = saveToObsidianVault(markdownContent, vaultUriString)

                    if (fileUri != null) {
                        // Success - update notification
                        updateNotification("YouTube Link Saved", "Metadata saved to Obsidian")

                        // Add link to today's note
                        addLinkToTodayNote(fileUri, metadata.title, vaultUriString)

                        // Show completion notification
                        showCompletionNotification(metadata.title, fileUri)

                        // Add where metadata is successfully saved
                        AnalyticsHelper.trackYouTubeExtraction(true)
                    } else {
                        updateNotification("Error", "Failed to save YouTube metadata")

                        // Add in error case
                        AnalyticsHelper.trackYouTubeExtraction(false)
                        AnalyticsHelper.trackError("youtube_extraction", "Failed to save YouTube metadata")
                    }
                } else {
                    // Failed to fetch metadata
                    val error = metadataResult.exceptionOrNull()?.message ?: "Unknown error"
                    updateNotification("Error", "Failed to fetch YouTube metadata: $error")
                }

                // Stop service after a short delay
                Thread.sleep(1000)
                stopSelf(startId)
            } catch (e: Exception) {
                Log.e(TAG, "Error processing YouTube link", e)
                updateNotification("Error", "Failed to process YouTube link: ${e.message}")
                stopSelf(startId)
            }
        }

        // Use the parent implementation which returns START_REDELIVER_INTENT
        return super.onStartCommand(intent, flags, startId)
    }

    private fun formatAsMarkdown(metadata: YouTubeMetadata, videoId: String, sharedText: String): String {
        val sb = StringBuilder()
        val currentDate = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault()).format(Date())

        // Add YAML frontmatter
        sb.append("---\n")
        sb.append("title: \"${metadata.title}\"\n")
        sb.append("date: $currentDate\n")
        sb.append("type: youtube\n")
        sb.append("tags: [youtube, video]\n")
        sb.append("source: \"https://www.youtube.com/watch?v=$videoId\"\n")
        sb.append("author: \"${metadata.uploader}\"\n")
        sb.append("---\n\n")

        // Add title and video link
        sb.append("# ${metadata.title}\n\n")

        // Add YouTube thumbnail as an image
        sb.append("![${metadata.title}](https://img.youtube.com/vi/$videoId/maxresdefault.jpg)\n\n")

        // Add video link
        sb.append("🔗 [Watch on YouTube](https://www.youtube.com/watch?v=$videoId)\n\n")

        // Add metadata
        sb.append("## Video Info\n\n")
        sb.append("- **Channel**: ${metadata.uploader}\n")
        sb.append("- **Published**: ${metadata.uploadDate}\n")
        sb.append("- **Video ID**: ${metadata.videoId}\n\n")

        // Add description
        sb.append("## Description\n\n")
        sb.append(metadata.description)

        // Add original shared text if available
        if (sharedText.isNotEmpty()) {
            sb.append("\n\n## Original Shared Text\n\n")
            sb.append(sharedText)
        }

        return sb.toString()
    }

    private fun saveToObsidianVault(content: String, vaultUriString: String): Uri? {
        try {
            val vaultUri = vaultUriString.toUri()
            val vaultDoc = DocumentFile.fromTreeUri(this, vaultUri) ?: return null

            // Create Clippings folder if needed
            var clippingsFolder = vaultDoc.findFile("Clippings")
            if (clippingsFolder == null) {
                clippingsFolder = vaultDoc.createDirectory("Clippings")
                if (clippingsFolder == null) {
                    Log.e(TAG, "Failed to create Clippings folder")
                    return null
                }
            }

            // Create a filename based on current date and time
            // Use the same format as OCR and Voice services (YouTube-YYYY-MM-DD-HHmmss.md)
            val timestamp = SimpleDateFormat("yyyy-MM-dd-HHmmss", Locale.US).format(Date())
            val fileName = "YouTube-$timestamp.md"

            // Create the file
            val file = clippingsFolder.createFile("text/markdown", fileName)
            if (file == null) {
                Log.e(TAG, "Failed to create file")
                return null
            }

            // Write content to the file
            contentResolver.openOutputStream(file.uri)?.use { outputStream ->
                outputStream.write(content.toByteArray())
            }

            return file.uri
        } catch (e: Exception) {
            Log.e(TAG, "Error saving to Obsidian vault", e)
            return null
        }
    }

    private fun showCompletionNotification(title: String, fileUri: Uri) {
        // Create a notification to show that the YouTube metadata is saved
        val notificationManager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager

        val notificationBuilder = NotificationCompat.Builder(this, NOTIFICATION_CHANNEL_ID)
            .setContentTitle("YouTube Note Saved")
            .setContentText(title)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setAutoCancel(true)

        // Create an intent to open the file in Obsidian
        val openIntent = Intent(Intent.ACTION_VIEW).apply {
            data = fileUri
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
        }

        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            openIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        notificationBuilder.setContentIntent(pendingIntent)

        // Show the notification
        notificationManager.notify(notificationId + 1000, notificationBuilder.build())
    }

    private fun addLinkToTodayNote(fileUri: Uri, title: String, vaultUriString: String) {
        // Start the TodayNoteService to add the link
        // This will create a collapsible callout with format:
        // > [!info]- YouTube at HH:mm
        // > 📺 ![[relativePath]]
        TodayNoteService.startLinkService(
            context = this,
            fileUri = fileUri.toString(),
            fileTitle = title,
            vaultUri = vaultUriString,
            fileType = "youtube"
        )
    }
}
