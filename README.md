# VaultSnap

VaultSnap is an Android application that helps you capture and organize content into your Obsidian vault. It provides features for text extraction from images (OCR), speech-to-text recording, YouTube content extraction, and seamless integration with Obsidian vaults.

## Features

- 📱 **Vault Integration**: Connect to your Obsidian vault and automatically organize content
- 🖼️ **OCR (Optical Character Recognition)**: Extract text from images using ML Kit
- 🎤 **Speech-to-Text**: Record audio and convert it to text with automatic transcription
- 📺 **YouTube Content**: Extract metadata and subtitles from YouTube videos
- 📅 **Calendar Integration**: Add events to daily notes with calendar app integration
- 🔄 **Share Intent**: Process shared content from other apps
- 📝 **Markdown Support**: Full markdown rendering and note reference styling

## Development

### Prerequisites

- Android Studio Arctic Fox or later
- JDK 17
- Android SDK with API level 35
- Ruby (for Fastlane automation)

### Building the Project

1. Clone the repository
2. Open in Android Studio
3. Sync the project
4. Build and run

### Fastlane Automation

This project uses Fastlane for build automation, testing, and deployment. 

#### Quick Setup

**Linux/macOS:**
```bash
./setup_fastlane.sh
```

**Windows:**
```batch
setup_fastlane.bat
```

#### Manual Setup

1. Install Ruby and Bundler
2. Install dependencies: `bundle install`
3. Install plugins: `bundle exec fastlane install_plugins`
4. Configure environment variables in `fastlane/.env.local`
5. Set up signing configuration in `local.properties`

#### Available Commands

- `bundle exec fastlane test` - Run all tests
- `bundle exec fastlane build_debug` - Build debug APK
- `bundle exec fastlane build_release` - Build release APK
- `bundle exec fastlane deploy_firebase` - Deploy to Firebase App Distribution
- `bundle exec fastlane deploy_internal` - Deploy to Google Play Internal Testing
- `bundle exec fastlane release` - Complete release process

For detailed Fastlane documentation, see [fastlane/README.md](fastlane/README.md).

### Project Structure

```
app/
├── src/main/java/com/devindie/keepsidian/
│   ├── activities/          # Activity classes
│   ├── ui/                  # Compose UI components
│   ├── data/                # Data layer (repositories, database)
│   ├── workers/             # Background workers
│   └── utils/               # Utility classes
├── src/test/                # Unit tests
└── src/androidTest/         # Instrumented tests

fastlane/                    # Fastlane configuration
├── Fastfile                 # Main Fastlane configuration
├── Appfile                  # App-specific settings
├── .env                     # Environment variables template
└── README.md                # Fastlane documentation
```

### Configuration

The app requires configuration of:

1. **Obsidian Vault**: Select your vault folder and configure attachment/daily notes paths
2. **Firebase**: For analytics and crash reporting (optional)
3. **Signing**: For release builds (required for distribution)

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests: `bundle exec fastlane test`
5. Submit a pull request

### CI/CD

The project includes GitHub Actions workflows for:
- Automated testing on pull requests
- Firebase App Distribution for develop branch
- Google Play deployment for main branch

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues and feature requests, please use the GitHub issue tracker.
