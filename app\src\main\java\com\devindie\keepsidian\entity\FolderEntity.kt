package com.devindie.keepsidian.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.devindie.keepsidian.presentation.theme.Blue
import com.devindie.keepsidian.presentation.theme.Cyan
import com.devindie.keepsidian.presentation.theme.Green
import com.devindie.keepsidian.presentation.theme.Orange
import com.devindie.keepsidian.presentation.theme.Purple
import com.devindie.keepsidian.presentation.theme.Red
import com.devindie.keepsidian.presentation.theme.Yellow
import kotlinx.serialization.Serializable

@Serializable
@Entity
data class FolderEntity(
    @PrimaryKey val id: Long? = null,
    val name: String = "",
    val color: Int? = null
) {
    companion object {
        val folderColors = listOf(
            <PERSON>,
            <PERSON>,
            <PERSON>,
            <PERSON>,
            <PERSON><PERSON>,
            <PERSON>,
            Purple
        )
    }
}