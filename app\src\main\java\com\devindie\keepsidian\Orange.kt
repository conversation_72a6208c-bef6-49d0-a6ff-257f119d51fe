package com.devindie.keepsidian

import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.ui.graphics.Color

private val primaryLight = Color(0xFF8B4F25)
private val onPrimaryLight = Color(0xFFFFFFFF)
private val primaryContainerLight = Color(0xFFFFDBC8)
private val onPrimaryContainerLight = Color(0xFF311300)
private val secondaryLight = Color(0xFF8B4F24)
private val onSecondaryLight = Color(0xFFFFFFFF)
private val secondaryContainerLight = Color(0xFFFFDBC7)
private val onSecondaryContainerLight = Color(0xFF311300)
private val tertiaryLight = Color(0xFF616118)
private val onTertiaryLight = Color(0xFFFFFFFF)
private val tertiaryContainerLight = Color(0xFFE7E78F)
private val onTertiaryContainerLight = Color(0xFF1D1D00)
private val errorLight = Color(0xFF904A43)
private val onErrorLight = Color(0xFFFFFFFF)
private val errorContainerLight = Color(0xFFFFDAD5)
private val onErrorContainerLight = Color(0xFF3B0907)
private val backgroundLight = Color(0xFFFFF8F5)
private val onBackgroundLight = Color(0xFF221A15)
private val surfaceLight = Color(0xFFFFF8F5)
private val onSurfaceLight = Color(0xFF221A15)
private val surfaceVariantLight = Color(0xFFF4DED3)
private val onSurfaceVariantLight = Color(0xFF52443C)
private val outlineLight = Color(0xFF84746B)
private val outlineVariantLight = Color(0xFFD7C2B8)
private val scrimLight = Color(0xFF000000)
private val inverseSurfaceLight = Color(0xFF382E29)
private val inverseOnSurfaceLight = Color(0xFFFFEDE5)
private val inversePrimaryLight = Color(0xFFFFB689)
private val surfaceDimLight = Color(0xFFE7D7CF)
private val surfaceBrightLight = Color(0xFFFFF8F5)
private val surfaceContainerLowestLight = Color(0xFFFFFFFF)
private val surfaceContainerLowLight = Color(0xFFFFF1EA)
private val surfaceContainerLight = Color(0xFFFCEBE2)
private val surfaceContainerHighLight = Color(0xFFF6E5DC)
private val surfaceContainerHighestLight = Color(0xFFF0DFD7)

private val primaryDark = Color(0xFFFFB689)
private val onPrimaryDark = Color(0xFF512300)
private val primaryContainerDark = Color(0xFF6E3810)
private val onPrimaryContainerDark = Color(0xFFFFDBC8)
private val secondaryDark = Color(0xFFFFB688)
private val onSecondaryDark = Color(0xFF512400)
private val secondaryContainerDark = Color(0xFF6E380F)
private val onSecondaryContainerDark = Color(0xFFFFDBC7)
private val tertiaryDark = Color(0xFFCBCB76)
private val onTertiaryDark = Color(0xFF323200)
private val tertiaryContainerDark = Color(0xFF494900)
private val onTertiaryContainerDark = Color(0xFFE7E78F)
private val errorDark = Color(0xFFFFB4AB)
private val onErrorDark = Color(0xFF561E19)
private val errorContainerDark = Color(0xFF73342D)
private val onErrorContainerDark = Color(0xFFFFDAD5)
private val backgroundDark = Color(0xFF19120D)
private val onBackgroundDark = Color(0xFFF0DFD7)
private val surfaceDark = Color(0xFF19120D)
private val onSurfaceDark = Color(0xFFF0DFD7)
private val surfaceVariantDark = Color(0xFF52443C)
private val onSurfaceVariantDark = Color(0xFFD7C2B8)
private val outlineDark = Color(0xFF9F8D83)
private val outlineVariantDark = Color(0xFF52443C)
private val scrimDark = Color(0xFF000000)
private val inverseSurfaceDark = Color(0xFFF0DFD7)
private val inverseOnSurfaceDark = Color(0xFF382E29)
private val inversePrimaryDark = Color(0xFF8B4F25)
private val surfaceDimDark = Color(0xFF19120D)
private val surfaceBrightDark = Color(0xFF413731)
private val surfaceContainerLowestDark = Color(0xFF140D08)
private val surfaceContainerLowDark = Color(0xFF221A15)
private val surfaceContainerDark = Color(0xFF261E19)
private val surfaceContainerHighDark = Color(0xFF312823)
private val surfaceContainerHighestDark = Color(0xFF3D332D)


val LightOrangeColors = lightColorScheme(
    primary = primaryLight,
    onPrimary = onPrimaryLight,
    primaryContainer = primaryContainerLight,
    onPrimaryContainer = onPrimaryContainerLight,
    secondary = secondaryLight,
    onSecondary = onSecondaryLight,
    secondaryContainer = secondaryContainerLight,
    onSecondaryContainer = onSecondaryContainerLight,
    tertiary = tertiaryLight,
    onTertiary = onTertiaryLight,
    tertiaryContainer = tertiaryContainerLight,
    onTertiaryContainer = onTertiaryContainerLight,
    error = errorLight,
    onError = onErrorLight,
    errorContainer = errorContainerLight,
    onErrorContainer = onErrorContainerLight,
    background = backgroundLight,
    onBackground = onBackgroundLight,
    surface = surfaceLight,
    onSurface = onSurfaceLight,
    surfaceVariant = surfaceVariantLight,
    onSurfaceVariant = onSurfaceVariantLight,
    outline = outlineLight,
    inverseOnSurface = inverseOnSurfaceLight,
    inverseSurface = inverseSurfaceLight,
    inversePrimary = inversePrimaryLight,
    outlineVariant = outlineVariantLight,
    scrim = scrimLight,
    surfaceContainerLowest = surfaceContainerLowestLight,
    surfaceContainerLow = surfaceContainerLowLight,
    surfaceContainer = surfaceContainerLight,
    surfaceContainerHigh = surfaceContainerHighLight,
    surfaceContainerHighest = surfaceContainerHighestLight,
    surfaceBright = surfaceBrightLight,
    surfaceDim = surfaceDimLight,
)


val DarkOrangeColors = darkColorScheme(
    primary = primaryDark,
    onPrimary = onPrimaryDark,
    primaryContainer = primaryContainerDark,
    onPrimaryContainer = onPrimaryContainerDark,
    secondary = secondaryDark,
    onSecondary = onSecondaryDark,
    secondaryContainer = secondaryContainerDark,
    onSecondaryContainer = onSecondaryContainerDark,
    tertiary = tertiaryDark,
    onTertiary = onTertiaryDark,
    tertiaryContainer = tertiaryContainerDark,
    onTertiaryContainer = onTertiaryContainerDark,
    error = errorDark,
    onError = onErrorDark,
    errorContainer = errorContainerDark,
    onErrorContainer = onErrorContainerDark,
    background = backgroundDark,
    onBackground = onBackgroundDark,
    surface = surfaceDark,
    onSurface = onSurfaceDark,
    surfaceVariant = surfaceVariantDark,
    onSurfaceVariant = onSurfaceVariantDark,
    outline = outlineDark,
    inverseOnSurface = inverseOnSurfaceDark,
    inverseSurface = inverseSurfaceDark,
    inversePrimary = inversePrimaryDark,
    outlineVariant = outlineVariantDark,
    scrim = scrimDark,
    surfaceContainerLowest = surfaceContainerLowestDark,
    surfaceContainerLow = surfaceContainerLowDark,
    surfaceContainer = surfaceContainerDark,
    surfaceContainerHigh = surfaceContainerHighDark,
    surfaceContainerHighest = surfaceContainerHighestDark,
    surfaceBright = surfaceBrightDark,
    surfaceDim = surfaceDimDark,
)
