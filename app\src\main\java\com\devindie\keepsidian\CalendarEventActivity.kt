package com.devindie.keepsidian

import android.app.DatePickerDialog
import android.app.TimePickerDialog
import android.content.Intent
import android.os.Bundle
import android.provider.CalendarContract
import android.widget.Button
import android.widget.CheckBox
import android.widget.EditText
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.devindie.keepsidian.analytics.AnalyticsHelper
import com.devindie.keepsidian.services.TodayNoteService
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale

class CalendarEventActivity : AppCompatActivity() {
    private lateinit var descriptionEditText: EditText
    private lateinit var dateTimeTextView: TextView
    private lateinit var selectDateTimeButton: Button
    private lateinit var createEventButton: Button
    private lateinit var cancelButton: Button
    private lateinit var addToTodayNoteCheckBox: CheckBox

    private val calendar = Calendar.getInstance()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_calendar_event)

        // Initialize views
        descriptionEditText = findViewById(R.id.description_edit_text)
        dateTimeTextView = findViewById(R.id.date_time_text_view)
        selectDateTimeButton = findViewById(R.id.select_date_time_button)
        createEventButton = findViewById(R.id.create_event_button)
        cancelButton = findViewById(R.id.cancel_button)
        addToTodayNoteCheckBox = findViewById(R.id.add_to_today_note_checkbox)

        // Set default time to current time + 1 hour
        calendar.add(Calendar.HOUR_OF_DAY, 1)
        updateDateTimeText()

        // Set up date time picker
        selectDateTimeButton.setOnClickListener {
            showDateTimePicker()
        }

        // Set up create event button
        createEventButton.setOnClickListener {
            createCalendarEvent()
        }

        // Set up cancel button
        cancelButton.setOnClickListener {
            finish()
        }
    }

    private fun showDateTimePicker() {
        // Show date picker
        DatePickerDialog(
            this,
            { _, year, month, dayOfMonth ->
                calendar.set(Calendar.YEAR, year)
                calendar.set(Calendar.MONTH, month)
                calendar.set(Calendar.DAY_OF_MONTH, dayOfMonth)

                // After date is set, show time picker
                TimePickerDialog(
                    this,
                    { _, hourOfDay, minute ->
                        calendar.set(Calendar.HOUR_OF_DAY, hourOfDay)
                        calendar.set(Calendar.MINUTE, minute)
                        updateDateTimeText()
                    },
                    calendar.get(Calendar.HOUR_OF_DAY),
                    calendar.get(Calendar.MINUTE),
                    false
                ).show()
            },
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH),
            calendar.get(Calendar.DAY_OF_MONTH)
        ).show()
    }

    private fun updateDateTimeText() {
        val dateFormat = SimpleDateFormat("EEE, MMM d, yyyy 'at' h:mm a", Locale.getDefault())
        dateTimeTextView.text = dateFormat.format(calendar.time)
    }

    private fun createCalendarEvent() {
        val description = descriptionEditText.text.toString().trim()

        if (description.isEmpty()) {
            Toast.makeText(this, getString(R.string.please_enter_event_description), Toast.LENGTH_SHORT).show()
            return
        }

        // Check if date/time was modified from the default (current time + 1 hour)
        val currentTime = Calendar.getInstance().timeInMillis
        val defaultTime = currentTime + (60 * 60 * 1000) // Current time + 1 hour
        val timeDifference = Math.abs(calendar.timeInMillis - defaultTime)
        val userSelectedTime = timeDifference > (60 * 1000) // If difference > 1 minute, user likely changed it

        // Check if the selected date is in the future
        val selectedDate = Calendar.getInstance()
        selectedDate.timeInMillis = calendar.timeInMillis

        val today = Calendar.getInstance()
        today.set(Calendar.HOUR_OF_DAY, 0)
        today.set(Calendar.MINUTE, 0)
        today.set(Calendar.SECOND, 0)
        today.set(Calendar.MILLISECOND, 0)

        val selectedDay = Calendar.getInstance()
        selectedDay.timeInMillis = calendar.timeInMillis
        selectedDay.set(Calendar.HOUR_OF_DAY, 0)
        selectedDay.set(Calendar.MINUTE, 0)
        selectedDay.set(Calendar.SECOND, 0)
        selectedDay.set(Calendar.MILLISECOND, 0)

        val isFutureDate = selectedDay.after(today)

        // Add to today's note and potentially to the future date's note
        addEventToTodayNote(description, calendar.timeInMillis, userSelectedTime, isFutureDate)

        // Only open calendar app if user selected a time
        if (userSelectedTime) {
            val startTime = calendar.timeInMillis
            val endTime = startTime + (10 * 60 * 1000) // Add 10 minutes

            // Create intent to add event to calendar
            val intent = Intent(Intent.ACTION_INSERT).apply {
                data = CalendarContract.Events.CONTENT_URI
                putExtra(CalendarContract.Events.TITLE, description)
                putExtra(CalendarContract.EXTRA_EVENT_BEGIN_TIME, startTime)
                putExtra(CalendarContract.EXTRA_EVENT_END_TIME, endTime)
                putExtra(CalendarContract.Events.ALL_DAY, false)
            }

            if (intent.resolveActivity(packageManager) != null) {
                startActivity(intent)
            } else {
                Toast.makeText(this, "No calendar app found", Toast.LENGTH_SHORT).show()
            }
        }

        finish()
    }

    private fun addEventToTodayNote(
        description: String,
        startTime: Long,
        hasSchedule: Boolean = true,
        addToFutureDate: Boolean = false
    ) {
        // Get vault URI from preferences
        val appDataStore = AppModule.provideAppDataStoreRepository(applicationContext)
        val vaultUriString = appDataStore.getStringValue(Constants.Preferences.OBSIDIAN_VAULT_URI, "")

        if (vaultUriString.isEmpty()) {
            Toast.makeText(this, "Vault not configured", Toast.LENGTH_SHORT).show()
            return
        }

        // Get the task status (done/undone)
        val isDone = addToTodayNoteCheckBox.isChecked

        // Start the today note service for calendar events
        TodayNoteService.startCalendarEventService(
            this,
            description,
            startTime,
            vaultUriString,
            isDone,
            hasSchedule,
            addToFutureDate
        )

        val toastMessage = if (addToFutureDate) {
            getString(R.string.adding_link_to_notes)
        } else {
            getString(R.string.adding_link_to_todays_note)
        }

        Toast.makeText(
            this,
            toastMessage,
            Toast.LENGTH_SHORT
        ).show()

        // Add to addEventToTodayNote method
        AnalyticsHelper.trackCalendarEventCreation(true, addToFutureDate)
    }
}
