<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/widget_background"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Today's Tasks"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="#FFFFFF" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginTop="8dp">
        
        <CheckBox
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="This is just a preview of the widget"
            android:textColor="#FFFFFF" />
            
        <CheckBox
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="You should resize or re-add your widget"
            android:textColor="#FFFFFF" />
            
        <CheckBox
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="To see the actual widget UI"
            android:checked="true"
            android:textColor="#FFFFFF" />
    </LinearLayout>
</LinearLayout>