<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Theme.ObsidianExt.NoActionBar" parent="Theme.ObsidianExt">
        <item name="android:windowActionBar">false</item>
        <item name="android:windowNoTitle">true</item>
    </style>

    <style name="Theme.ObsidianExt.Transparent" parent="Theme.ObsidianExt.NoActionBar">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>
</resources>
