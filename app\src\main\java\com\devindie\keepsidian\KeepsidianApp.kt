package com.devindie.keepsidian

import android.app.Application
import com.devindie.keepsidian.analytics.AnalyticsHelper
import com.google.firebase.FirebaseApp

class KeepsidianApp : Application() {
    
    override fun onCreate() {
        super.onCreate()
        
        // Initialize Firebase
        FirebaseApp.initializeApp(this)
        
        // Initialize Analytics Helper
        AnalyticsHelper.init(this)
        
        // Track app startup time
        val startupTime = System.currentTimeMillis() - ProcessStartTime.startTime
        AnalyticsHelper.trackAppStartup(startupTime)
    }
}

