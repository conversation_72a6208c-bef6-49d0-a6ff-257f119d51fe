package com.devindie.keepsidian

import android.content.Context
import androidx.room.Room
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object AppModule {

    @Singleton
    @Provides
    fun provideAppDataStoreRepository(@ApplicationContext context: Context): AppDataStoreRepository =
        AppDataStoreRepositoryImpl(context)

    @Singleton
    @Provides
    fun provideWidgetDataStoreRepository(@ApplicationContext context: Context): WidgetDataStoreRepository =
        WidgetDataStoreRepositoryImpl(context)

    @Provides
    @Singleton
    fun provideNoteDatabase(@ApplicationContext context: Context): Database =
        Room.databaseBuilder(
            context,
            Database::class.java,
            Database.NAME
        ).addMigrations(MIGRATION_1_2).build()

    @Provides
    @Singleton
    fun provideNoteRepository(database: Database): NoteRepository =
        NoteRepositoryImpl(dao = database.noteDao)


}
