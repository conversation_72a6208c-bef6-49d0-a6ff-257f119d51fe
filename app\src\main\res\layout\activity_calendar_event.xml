<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/add_calendar_event"
        android:textSize="20sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/task_description"
        android:layout_marginBottom="8dp" />

    <EditText
        android:id="@+id/description_edit_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="@string/enter_task_description"
        android:inputType="textCapSentences"
        android:layout_marginBottom="16dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/task_date_time"
        android:layout_marginBottom="8dp" />

    <TextView
        android:id="@+id/date_time_text_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="16sp"
        android:layout_marginBottom="8dp" />

    <Button
        android:id="@+id/select_date_time_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Select Date and Time"
        android:layout_marginBottom="16dp" />

    <CheckBox
        android:id="@+id/add_to_today_note_checkbox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/task_mark_as_done"
        android:checked="false"
        android:layout_marginBottom="24dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <Button
            android:id="@+id/cancel_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Cancel"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/create_event_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/create_task"
            android:layout_marginStart="8dp" />
    </LinearLayout>
</LinearLayout>
