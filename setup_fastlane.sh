#!/bin/bash

# VaultSnap Fastlane Setup Script
# This script helps set up Fastlane for the VaultSnap Android project

echo "🚀 Setting up Fastlane for VaultSnap..."

# Check if Ruby is installed
if ! command -v ruby &> /dev/null; then
    echo "❌ Ruby is not installed. Please install Ruby first."
    echo "   Visit: https://www.ruby-lang.org/en/documentation/installation/"
    exit 1
fi

echo "✅ Ruby found: $(ruby --version)"

# Check if <PERSON><PERSON><PERSON> is installed
if ! command -v bundle &> /dev/null; then
    echo "📦 Installing Bundler..."
    gem install bundler
else
    echo "✅ Bund<PERSON> found: $(bundle --version)"
fi

# Install Ruby dependencies
echo "📦 Installing Ruby dependencies..."
bundle install

# Install Fastlane plugins
echo "🔌 Installing Fastlane plugins..."
bundle exec fastlane install_plugins

# Create .env.local from template
if [ ! -f "fastlane/.env.local" ]; then
    echo "📝 Creating .env.local from template..."
    cp fastlane/.env fastlane/.env.local
    echo "⚠️  Please edit fastlane/.env.local with your actual credentials"
else
    echo "✅ .env.local already exists"
fi

# Create local.properties from template if it doesn't exist
if [ ! -f "local.properties" ]; then
    echo "📝 Creating local.properties from template..."
    cp local.properties.template local.properties
    echo "⚠️  Please edit local.properties with your SDK path and signing configuration"
else
    echo "✅ local.properties already exists"
fi

echo ""
echo "🎉 Fastlane setup complete!"
echo ""
echo "Next steps:"
echo "1. Edit fastlane/.env.local with your Firebase and Google Play credentials"
echo "2. Edit local.properties with your signing configuration"
echo "3. Create a keystore for release signing"
echo "4. Test the setup with: bundle exec fastlane build_debug"
echo ""
echo "Available commands:"
echo "  bundle exec fastlane test           - Run tests"
echo "  bundle exec fastlane build_debug    - Build debug APK"
echo "  bundle exec fastlane build_release  - Build release APK"
echo "  bundle exec fastlane deploy_firebase - Deploy to Firebase"
echo ""
echo "For more information, see fastlane/README.md"
