package com.devindie.keepsidian

import android.content.Context
import android.util.Log
import io.ktor.client.HttpClient
import io.ktor.client.request.get
import io.ktor.client.statement.bodyAsText
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.regex.Pattern

/**
 * Service for fetching YouTube metadata using Ktor HTTP client
 */
class YouTubeService(private val httpClient: HttpClient, private val context: Context) {
    companion object {
        private const val TAG = "YouTubeService"

        // Pattern to extract title from HTML
        private val TITLE_PATTERN = Pattern.compile("<title>(.*?)</title>", Pattern.DOTALL)

        // Pattern to extract channel name
        private val CHANNEL_PATTERN =
            Pattern.compile("\"ownerChannelName\":\"(.*?)\"", Pattern.DOTALL)

        // Pattern to extract description
        private val DESCRIPTION_PATTERN =
            Pattern.compile("\"description\":\\{\"simpleText\":\"(.*?)\"\\}", Pattern.DOTALL)

        // Pattern to extract upload date
        private val UPLOAD_DATE_PATTERN =
            Pattern.compile("\"dateText\":\\{\"simpleText\":\"(.*?)\"\\}", Pattern.DOTALL)
    }

    /**
     * Fetches YouTube metadata for a video ID
     */
    suspend fun getYouTubeMetadata(videoId: String): Result<YouTubeMetadata> =
        withContext(Dispatchers.IO) {
            try {
                // First try to get metadata from oEmbed API
                val oEmbedUrl =
                    "https://www.youtube.com/oembed?url=https://www.youtube.com/watch?v=$videoId&format=json"
                val oEmbedResponse = try {
                    val response = httpClient.get(oEmbedUrl).bodyAsText()
                    val jsonObject = JSONObject(response)
                    OEmbedResponse(
                        title = jsonObject.optString("title", "Unknown Title"),
                        authorName = jsonObject.optString("author_name", "Unknown Channel"),
                        html = jsonObject.optString("html", ""),
                        thumbnailUrl = jsonObject.optString("thumbnail_url", "")
                    )
                } catch (e: Exception) {
                    Log.e(TAG, "Error fetching oEmbed data", e)
                    null
                }

                // Get the page HTML for additional metadata
                val pageUrl = "https://www.youtube.com/watch?v=$videoId"
                val pageHtml = try {
                    httpClient.get(pageUrl).bodyAsText()
                } catch (e: Exception) {
                    Log.e(TAG, "Error fetching page HTML", e)
                    ""
                }

                // Extract title
                val title = oEmbedResponse?.title ?: extractFromPattern(pageHtml, TITLE_PATTERN)
                    ?.replace(" - YouTube", "") ?: "Unknown Title"

                // Extract channel name
                val channelName =
                    oEmbedResponse?.authorName ?: extractFromPattern(pageHtml, CHANNEL_PATTERN)
                    ?: "Unknown Channel"

                // Extract description
                val description = extractFromPattern(pageHtml, DESCRIPTION_PATTERN) ?: ""

                // Extract upload date
                val uploadDateStr = extractFromPattern(pageHtml, UPLOAD_DATE_PATTERN)
                val uploadDate = if (uploadDateStr != null) {
                    try {
                        // Try to parse the date from the page
                        // This is a simplification - actual implementation would need to handle various date formats
                        SimpleDateFormat("yyyy-MM-dd", Locale.US).format(Date())
                    } catch (e: Exception) {
                        Log.e(TAG, "Error parsing date", e)
                        SimpleDateFormat("yyyy-MM-dd", Locale.US).format(Date())
                    }
                } else {
                    // Default to current date if we can't extract it
                    SimpleDateFormat("yyyy-MM-dd", Locale.US).format(Date())
                }

                // Since we removed NewPipeExtractor, we can't extract subtitles
                val subtitles = ""

                // Create and return the metadata object
                val metadata = YouTubeMetadata(
                    videoId = videoId,
                    title = title,
                    uploader = channelName,
                    uploadDate = uploadDate,
                    description = description,
                    thumbnailUrl = oEmbedResponse?.thumbnailUrl ?: "",
                    subtitles = subtitles
                )
                Log.e(TAG, "metadata: $metadata")
                Result.success(metadata)
            } catch (e: Exception) {
                Log.e(TAG, "Error fetching YouTube metadata", e)
                Result.failure(e)
            }
        }

    private fun extractFromPattern(html: String, pattern: Pattern): String? {
        val matcher = pattern.matcher(html)
        return if (matcher.find()) {
            matcher.group(1)
        } else {
            null
        }
    }
}

/**
 * Data class for YouTube metadata
 */
data class YouTubeMetadata(
    val videoId: String,
    val title: String,
    val uploader: String,
    val description: String,
    val uploadDate: String,
    val thumbnailUrl: String,
    val subtitles: String
)

/**
 * Data class for oEmbed response
 */
data class OEmbedResponse(
    val title: String,
    val authorName: String,
    val html: String,
    val thumbnailUrl: String
)
