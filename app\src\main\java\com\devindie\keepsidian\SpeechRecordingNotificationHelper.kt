package com.devindie.keepsidian

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.util.Log
import androidx.core.app.NotificationCompat

/**
 * Helper class to show a notification when speech recording is complete
 */
object SpeechRecordingNotificationHelper {
    private const val TAG = "SpeechRecordingNotificationHelper"
    private const val NOTIFICATION_CHANNEL_ID = "obsidian_ext_channel"
    private const val NOTIFICATION_ID = 6

    /**
     * Shows a notification when both audio saving and transcription are complete
     */
    fun showCompletionNotification(context: Context, fileUri: Uri) {
        // Check if we have notification permission
        if (!NotificationPermissionHelper.hasNotificationPermission(context)) {
            Log.d(TAG, "Notification permission not granted, skipping notification")
            return
        }

        val notificationManager =
            context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        // Create notification channel for Android O and above
        val channel = NotificationChannel(
            NOTIFICATION_CHANNEL_ID, context.getString(R.string.notification_channel_name), NotificationManager.IMPORTANCE_DEFAULT
        )
        notificationManager.createNotificationChannel(channel)

        // Create intent to open the file in Obsidian
        val intent = Intent(context, OpenObsidianActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
            data = fileUri
        }

        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // Build the notification
        val notification = NotificationCompat.Builder(context, NOTIFICATION_CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentTitle(context.getString(R.string.voice_note_saved))
            .setContentText(context.getString(R.string.audio_transcription_saved))
            .setAutoCancel(true)
            .setContentIntent(pendingIntent)
            .build()

        // Show the notification
        notificationManager.notify(NOTIFICATION_ID, notification)
    }
}
