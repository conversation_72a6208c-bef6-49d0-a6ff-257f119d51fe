package com.devindie.keepsidian.utils

import android.content.Context
import androidx.glance.appwidget.GlanceAppWidgetManager
import com.devindie.keepsidian.DailyToDoWidget
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

object WidgetUtils {
    /**
     * Refreshes all instances of DailyTodoWidget
     */
    suspend fun refreshDailyTodoWidgets(context: Context) = withContext(Dispatchers.IO) {
        val glanceAppWidgetManager = GlanceAppWidgetManager(context)
        val glanceIds = glanceAppWidgetManager.getGlanceIds(DailyToDoWidget::class.java)
        val widget = DailyToDoWidget()

        // Update each widget instance
        glanceIds.forEach { glanceId ->
            widget.update(context, glanceId)
        }
    }
}
