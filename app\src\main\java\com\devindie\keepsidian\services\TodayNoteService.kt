package com.devindie.keepsidian.services

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.util.Log
import androidx.core.net.toUri
import androidx.documentfile.provider.DocumentFile
import com.devindie.keepsidian.AppModule
import com.devindie.keepsidian.Constants
import com.devindie.keepsidian.analytics.AnalyticsHelper
import com.devindie.keepsidian.utils.WidgetUtils
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.Date
import java.util.Locale

class TodayNoteService : BaseProcessingService() {
    companion object {
        private const val TAG = "TodayNoteService"
        private const val NOTIFICATION_ID = 1005

        const val EXTRA_FILE_URI = "com.devindie.keepsidian.extra.FILE_URI"
        const val EXTRA_FILE_TITLE = "com.devindie.keepsidian.extra.FILE_TITLE"
        const val EXTRA_VAULT_URI = "com.devindie.keepsidian.extra.VAULT_URI"
        const val EXTRA_FILE_TYPE = "com.devindie.keepsidian.extra.FILE_TYPE"
        const val EXTRA_ENTRY_TYPE = "com.devindie.keepsidian.extra.ENTRY_TYPE"
        const val EXTRA_EVENT_DESCRIPTION = "com.devindie.keepsidian.extra.EVENT_DESCRIPTION"
        const val EXTRA_EVENT_START_TIME = "com.devindie.keepsidian.extra.EVENT_START_TIME"
        const val EXTRA_TASK_IS_DONE = "com.devindie.keepsidian.extra.TASK_IS_DONE"
        const val EXTRA_TASK_HAS_SCHEDULE = "com.devindie.keepsidian.extra.TASK_HAS_SCHEDULE"
        const val EXTRA_ADD_TO_FUTURE_DATE = "com.devindie.keepsidian.extra.ADD_TO_FUTURE_DATE"

        const val TYPE_LINK = "link"
        const val TYPE_CALENDAR_EVENT = "calendar_event"

        // Icon prefixes for different file types
        const val PREFIX_VOICE = "🎤"
        const val PREFIX_IMAGE = "📷"
        const val PREFIX_OCR = "👁️"
        const val PREFIX_YOUTUBE = "📺"
        const val PREFIX_NOTE = "📝"
        const val CALENDAR_EVENT_PREFIX = "📅"
        const val UNDO_TASK_PREFIX = "⏰"

        fun startLinkService(
            context: Context,
            fileUri: String,
            fileTitle: String,
            vaultUri: String,
            fileType: String
        ) {
            val intent = Intent(context, TodayNoteService::class.java).apply {
                putExtra(EXTRA_FILE_URI, fileUri)
                putExtra(EXTRA_FILE_TITLE, fileTitle)
                putExtra(EXTRA_VAULT_URI, vaultUri)
                putExtra(EXTRA_FILE_TYPE, fileType)
                putExtra(EXTRA_ENTRY_TYPE, TYPE_LINK)
            }

            context.startForegroundService(intent)
        }

        fun startCalendarEventService(
            context: Context,
            eventDescription: String,
            eventStartTime: Long,
            vaultUri: String,
            isDone: Boolean = false,
            hasSchedule: Boolean = true,
            addToFutureDate: Boolean = false
        ) {
            val intent = Intent(context, TodayNoteService::class.java).apply {
                putExtra(EXTRA_EVENT_DESCRIPTION, eventDescription)
                putExtra(EXTRA_EVENT_START_TIME, eventStartTime)
                putExtra(EXTRA_VAULT_URI, vaultUri)
                putExtra(EXTRA_ENTRY_TYPE, TYPE_CALENDAR_EVENT)
                putExtra(EXTRA_TASK_IS_DONE, isDone)
                putExtra(EXTRA_TASK_HAS_SCHEDULE, hasSchedule)
                putExtra(EXTRA_ADD_TO_FUTURE_DATE, addToFutureDate)
            }

            context.startForegroundService(intent)
        }
    }

    override val serviceName: String = TAG
    override val notificationId: Int = NOTIFICATION_ID

    override fun onStartCommand(intent: Intent, flags: Int, startId: Int): Int {
        val entryType = intent.getStringExtra(EXTRA_ENTRY_TYPE) ?: ""
        val vaultUriString = intent.getStringExtra(EXTRA_VAULT_URI) ?: ""

        if (entryType.isEmpty() || vaultUriString.isEmpty()) {
            stopSelf(startId)
            return super.onStartCommand(intent, flags, startId)
        }

        // Start as foreground service
        startForeground(
            notificationId, createNotification(
                "Updating Today's Note",
                "Adding entry to today's note..."
            )
        )

        // Process in background
        serviceScope.launch {
            try {
                // Get daily notes folder from preferences
                val appDataStore = AppModule.provideAppDataStoreRepository(applicationContext)
                val dailyNotesFolder = appDataStore.getStringValue(
                    Constants.Preferences.OBSIDIAN_DAILY_NOTES_FOLDER,
                    ""
                )

                // Process based on entry type
                val success = when (entryType) {
                    TYPE_LINK -> processLinkEntry(intent, vaultUriString, dailyNotesFolder)
                    TYPE_CALENDAR_EVENT -> processCalendarEventEntry(
                        intent,
                        vaultUriString,
                        dailyNotesFolder
                    )

                    else -> {
                        Log.e(TAG, "Unknown entry type: $entryType")
                        false
                    }
                }

                if (success) {
                    updateNotification("Today's Note Updated", "Entry added successfully")
                    // Refresh widgets
                    WidgetUtils.refreshDailyTodoWidgets(this@TodayNoteService)
                } else {
                    updateNotification("Error", "Failed to update today's note")
                }

                // Stop service after a short delay
                Thread.sleep(1000)
                stopSelf(startId)
            } catch (e: Exception) {
                Log.e(TAG, "Error updating today's note", e)
                updateNotification("Error", "Failed to update today's note: ${e.message}")
                stopSelf(startId)
            }
        }

        // Use the parent implementation which returns START_REDELIVER_INTENT
        return super.onStartCommand(intent, flags, startId)
    }

    private fun processLinkEntry(
        intent: Intent?,
        vaultUriString: String,
        dailyNotesFolder: String
    ): Boolean {
        val fileUriString = intent?.getStringExtra(EXTRA_FILE_URI) ?: return false
        intent.getStringExtra(EXTRA_FILE_TITLE) ?: "Untitled"
        Log.e(
            "intent.getStringExtra(EXTRA_FILE_TYPE)",
            intent.getStringExtra(EXTRA_FILE_TYPE).toString()
        )
        val fileType = intent.getStringExtra(EXTRA_FILE_TYPE) ?: "note"

        // Get today's note file
        val todayFile = getTodayNoteFile(vaultUriString, dailyNotesFolder) ?: return false

        // Get the relative path of the file in the vault
        val fileUri = fileUriString.toUri()
        Log.d(TAG, "Getting relative path for file: $fileUriString")
        val relativePath = getRelativePathInVault(fileUri, vaultUriString)

        if (relativePath == null) {
            Log.e(TAG, "Failed to get relative path for file: $fileUriString")
            return false
        }

        Log.d(TAG, "Found relative path: $relativePath")

        // Get the appropriate prefix based on file type
        val prefix = when (fileType.lowercase()) {
            "voice","voice-offline" -> PREFIX_VOICE
            "image" -> PREFIX_IMAGE
            "ocr" -> PREFIX_OCR
            "youtube" -> PREFIX_YOUTUBE
            else -> PREFIX_NOTE
        }

        // Get current time for the entry
        val readableTime = SimpleDateFormat("HH:mm", Locale.getDefault()).format(Date())
        val typeLabel = when (fileType) {
            "voice-offline" -> "Voice Note"
            "voice" -> "Voice"
            "image" -> "Image"
            "ocr" -> "OCR Scan"
            "youtube" -> "YouTube"
            else -> "Note"
        }

        // Read the file content
        val content =
            contentResolver.openInputStream(todayFile.uri)?.use { it.bufferedReader().readText() }
                ?: ""

        // Create the link entry as a collapsible callout
        // Note: Callouts in Markdown require empty lines before and after to render correctly
        val linkEntry = "> [!info]- $typeLabel at $readableTime\n> $prefix ![[${relativePath}]]"

        // Append the link to the content with proper spacing for callout rendering
        val newContent = if (content.isEmpty()) {
            // If content is empty, just add the callout with a newline at the end
            "$linkEntry\n"
        } else if (content.endsWith("\n\n")) {
            // If content already ends with two newlines, add the callout with a newline at the end
            "$content$linkEntry\n\n"
        } else if (content.endsWith("\n")) {
            // If content ends with one newline, add another newline before the callout and two after
            "$content\n$linkEntry\n\n"
        } else {
            // If content doesn't end with newlines, add two newlines before and after the callout
            "$content\n\n$linkEntry\n\n"
        }

        // Write back to file
        contentResolver.openOutputStream(todayFile.uri)?.use { outputStream ->
            outputStream.write(newContent.toByteArray())
        }

        // Track note creation
        AnalyticsHelper.trackNoteCreation(fileType, true)

        return true
    }

    private fun processCalendarEventEntry(
        intent: Intent?,
        vaultUriString: String,
        dailyNotesFolder: String
    ): Boolean {
        val taskDescription = intent?.getStringExtra(EXTRA_EVENT_DESCRIPTION) ?: return false
        val eventStartTime = intent.getLongExtra(EXTRA_EVENT_START_TIME, 0)
        val isDone = intent.getBooleanExtra(EXTRA_TASK_IS_DONE, false)
        val hasSchedule = intent.getBooleanExtra(EXTRA_TASK_HAS_SCHEDULE, true)
        val addToFutureDate = intent.getBooleanExtra(EXTRA_ADD_TO_FUTURE_DATE, false)
        if (eventStartTime == 0L) return false

        // Get today's note file
        val todayFile = getTodayNoteFile(vaultUriString, dailyNotesFolder) ?: return false

        // Add to today's note
        val todaySuccess = addTaskToNoteFile(
            todayFile,
            taskDescription,
            eventStartTime,
            isDone,
            hasSchedule,
            false // Not an undo task in today's note
        )

        // Track task creation
        AnalyticsHelper.trackTaskCreation(true)

        // If we need to add to the future date as well
        if (addToFutureDate && hasSchedule) {
            // Convert eventStartTime to LocalDate
            val calendar = java.util.Calendar.getInstance()
            calendar.timeInMillis = eventStartTime
            val eventDate = LocalDate.of(
                calendar.get(java.util.Calendar.YEAR),
                calendar.get(java.util.Calendar.MONTH) + 1,
                calendar.get(java.util.Calendar.DAY_OF_MONTH)
            )

            // Get today's date
            val today = LocalDate.now()

            // Only add to future date if it's different from today
            if (eventDate.isAfter(today)) {
                // Get the note file for the event date
                val eventDateFile = getNoteFileForDate(vaultUriString, dailyNotesFolder, eventDate)
                if (eventDateFile != null) {
                    val markedAsNotDone = false // Set to false for the future date's note
                    // Add as an undo task to the future date's note
                    val futureSuccess = addTaskToNoteFile(
                        eventDateFile,
                        taskDescription,
                        eventStartTime,
                        markedAsNotDone,
                        hasSchedule,
                        true // This is an undo task in the future date's note
                    )

                    // Return true only if both operations succeeded
                    return todaySuccess && futureSuccess
                }
            }
        }

        return todaySuccess
    }

    /**
     * Adds a task to a specific note file
     *
     * @param noteFile The DocumentFile representing the note
     * @param taskDescription The description of the task
     * @param eventStartTime The start time of the event in milliseconds
     * @param isDone Whether the task is marked as done
     * @param hasSchedule Whether the task has a schedule
     * @param isUndoTask Whether this is an undo task (for future dates)
     * @return True if the task was successfully added, false otherwise
     */
    private fun addTaskToNoteFile(
        noteFile: DocumentFile,
        taskDescription: String,
        eventStartTime: Long,
        isDone: Boolean,
        hasSchedule: Boolean,
        isUndoTask: Boolean
    ): Boolean {
        try {
            // Read the file content
            val content =
                contentResolver.openInputStream(noteFile.uri)?.use { it.bufferedReader().readText() }
                    ?: ""

            // Create the task entry with the appropriate format
            val checkboxStatus = if (isDone) "x" else " "
            val taskEntry: String

            if (hasSchedule) {
                // Format the event date and time
                val eventDateTime = SimpleDateFormat(
                    "yyyy-MM-dd HH:mm",
                    Locale.getDefault()
                ).format(Date(eventStartTime))

                // Create a calendar URI for this event
                val calendarUri = createCalendarViewUri(taskDescription, eventStartTime)

                // Add the appropriate prefix based on whether it's an undo task
                val prefix = if (isUndoTask) UNDO_TASK_PREFIX else CALENDAR_EVENT_PREFIX

                // Format: - [(done/undone)] [prefix] task description [scheduled on date and time calendar]
                taskEntry =
                    "- [$checkboxStatus] $prefix $taskDescription [scheduled on $eventDateTime]($calendarUri)"
            } else {
                // Simple task without schedule
                // Format: - [(done/undone)] task description
                taskEntry = "- [$checkboxStatus] $taskDescription"
            }

            // Append the task to the content with proper spacing
            val newContent = if (content.isEmpty()) {
                // If content is empty, just add the task with a newline at the end
                "$taskEntry\n"
            } else if (content.endsWith("\n\n")) {
                // If content already ends with two newlines, add the task with a newline at the end
                "$content$taskEntry\n"
            } else if (content.endsWith("\n")) {
                // If content ends with one newline, add another newline before the task
                "$content\n$taskEntry\n"
            } else {
                // If content doesn't end with newlines, add two newlines before the task
                "$content\n\n$taskEntry\n"
            }

            // Write back to file
            contentResolver.openOutputStream(noteFile.uri)?.use { outputStream ->
                outputStream.write(newContent.toByteArray())
            }

            return true
        } catch (e: Exception) {
            Log.e(TAG, "Error adding task to note file", e)
            return false
        }
    }

    private fun getTodayNoteFile(vaultUriString: String, dailyNotesFolder: String): DocumentFile? {
        return getNoteFileForDate(vaultUriString, dailyNotesFolder, LocalDate.now())
    }

    /**
     * Gets or creates a note file for a specific date
     *
     * @param vaultUriString The URI string of the vault
     * @param dailyNotesFolder The folder path for daily notes
     * @param date The date for which to get the note file
     * @return The DocumentFile for the specified date's note, or null if it couldn't be created
     */
    private fun getNoteFileForDate(vaultUriString: String, dailyNotesFolder: String, date: LocalDate): DocumentFile? {
        try {
            val appDataStore = AppModule.provideAppDataStoreRepository(applicationContext)
            val vaultUri = vaultUriString.toUri()
            val vaultDoc = DocumentFile.fromTreeUri(this, vaultUri) ?: return null

            // Get the date using the configured format
            val dailyNotesFormat = appDataStore.getStringValue(
                Constants.Preferences.OBSIDIAN_DAILY_NOTES_FORMAT,
                "yyyy-MM-dd"
            )
            val formattedDate =
                date.format(DateTimeFormatter.ofPattern(dailyNotesFormat))

            // Find or create the daily notes folder
            var dailyNotesDir = vaultDoc
            if (dailyNotesFolder.isNotEmpty()) {
                val folderParts = dailyNotesFolder.split("/")
                for (part in folderParts) {
                    if (part.isEmpty()) continue

                    var folder = dailyNotesDir.findFile(part)
                    if (folder == null) {
                        folder = dailyNotesDir.createDirectory(part)
                        if (folder == null) {
                            Log.e(TAG, "Failed to create folder: $part")
                            return null
                        }
                    }
                    dailyNotesDir = folder
                }
            }

            // Check if the note exists
            val fileName = "$formattedDate.md"
            var noteFile = dailyNotesDir.findFile(fileName)

            // Create the note if it doesn't exist
            if (noteFile == null) {
                noteFile = dailyNotesDir.createFile("text/markdown", fileName)
                if (noteFile == null) {
                    Log.e(TAG, "Failed to create note for date: $formattedDate")
                    return null
                }

                // Initialize with a title
                val initialContent = "# $formattedDate\n\n"
                contentResolver.openOutputStream(noteFile.uri)?.use { outputStream ->
                    outputStream.write(initialContent.toByteArray())
                }
            }

            return noteFile
        } catch (e: Exception) {
            Log.e(TAG, "Error getting note file for date", e)
            return null
        }
    }

    private fun getRelativePathInVault(fileUri: Uri, vaultUriString: String): String? {
        try {
            Log.d(TAG, "Getting relative path for URI: $fileUri in vault: $vaultUriString")

            // Get the document file for the specific URI
            val docFile = DocumentFile.fromSingleUri(this, fileUri)
            if (docFile == null) {
                Log.e(TAG, "Failed to get document file from URI: $fileUri")
                return null
            }

            // Get the file name
            val fileName = docFile.name
            if (fileName == null) {
                Log.e(TAG, "Failed to get file name from document file")
                return null
            }

            Log.d(TAG, "File name: $fileName")

            // Get the save location from preferences
            val appDataStore = AppModule.provideAppDataStoreRepository(applicationContext)
            val saveLocation = appDataStore.getStringValue(
                Constants.Preferences.OBSIDIAN_SAVE_LOCATION,
                "Clippings"
            )

            // Determine the file type based on the file name pattern
            if (fileName.startsWith("YouTube-") && fileName.endsWith(".md")) {
                // YouTube files are saved in the save location (usually "Clippings")
                Log.d(TAG, "Identified as YouTube file, using path: $saveLocation/$fileName")
                return "$saveLocation/$fileName"
            } else if (fileName.startsWith("OCR-") && fileName.endsWith(".md")) {
                // OCR files are saved in the save location (usually "Clippings")
                Log.d(TAG, "Identified as OCR file, using path: $saveLocation/$fileName")
                return "$saveLocation/$fileName"
            } else if (fileName.startsWith("Voice-") && fileName.endsWith(".md")) {
                // Voice files are saved in the save location (usually "Clippings")
                Log.d(TAG, "Identified as Voice file, using path: $saveLocation/$fileName")
                return "$saveLocation/$fileName"
            } else if (fileName.startsWith("Note-") && fileName.endsWith(".md")) {
                // Note files are saved in the save location (usually "Clippings")
                Log.d(TAG, "Identified as Note file, using path: $saveLocation/$fileName")
                return "$saveLocation/$fileName"
            } else if (fileName.startsWith("image-") || fileName.endsWith(".jpg") || fileName.endsWith(
                    ".png"
                )
            ) {
                // Image files are saved in the attachment folder
                val attachmentFolder = appDataStore.getStringValue(
                    Constants.Preferences.OBSIDIAN_ATTACHMENT_FOLDER,
                    ""
                )
                if (attachmentFolder.isNotEmpty()) {
                    // Process the path - if it starts with a dot, it's relative to the vault root
                    val processedPath = if (attachmentFolder.startsWith(".")) {
                        // Remove the leading dot and slash if present
                        attachmentFolder.removePrefix("./").removePrefix(".")
                    } else {
                        attachmentFolder
                    }
                    Log.d(TAG, "Identified as image file, using path: $processedPath/$fileName")
                    return "$processedPath/$fileName"
                } else {
                    // Default to "attachments" folder if no specific path is set
                    Log.d(
                        TAG,
                        "Identified as image file, using default path: attachments/$fileName"
                    )
                    return "attachments/$fileName"
                }
            } else if (fileName.endsWith(".mp3") || fileName.endsWith(".wav") || fileName.endsWith(".m4a")) {
                // Audio files are saved in the attachment folder
                val attachmentFolder = appDataStore.getStringValue(
                    Constants.Preferences.OBSIDIAN_ATTACHMENT_FOLDER,
                    ""
                )
                if (attachmentFolder.isNotEmpty()) {
                    // Process the path - if it starts with a dot, it's relative to the vault root
                    val processedPath = if (attachmentFolder.startsWith(".")) {
                        // Remove the leading dot and slash if present
                        attachmentFolder.removePrefix("./").removePrefix(".")
                    } else {
                        attachmentFolder
                    }
                    Log.d(TAG, "Identified as audio file, using path: $processedPath/$fileName")
                    return "$processedPath/$fileName"
                } else {
                    // Default to "attachments" folder if no specific path is set
                    Log.d(
                        TAG,
                        "Identified as audio file, using default path: attachments/$fileName"
                    )
                    return "attachments/$fileName"
                }
            }

            // If we couldn't determine the path based on file patterns, try to verify if the file exists in the save location
            val vaultUri = vaultUriString.toUri()
            val vaultDoc = DocumentFile.fromTreeUri(this, vaultUri)
            if (vaultDoc != null) {
                val saveLocationFolder = vaultDoc.findFile(saveLocation)
                if (saveLocationFolder != null && saveLocationFolder.isDirectory) {
                    val file = saveLocationFolder.findFile(fileName)
                    if (file != null && file.isFile) {
                        Log.d(TAG, "Found file in $saveLocation folder: $saveLocation/$fileName")
                        return "$saveLocation/$fileName"
                    }
                }

                // Check in attachment folder as a last resort
                val attachmentFolder = appDataStore.getStringValue(
                    Constants.Preferences.OBSIDIAN_ATTACHMENT_FOLDER,
                    ""
                )
                if (attachmentFolder.isNotEmpty()) {
                    // Process the path - if it starts with a dot, it's relative to the vault root
                    val processedPath = if (attachmentFolder.startsWith(".")) {
                        // Remove the leading dot and slash if present
                        attachmentFolder.removePrefix("./").removePrefix(".")
                    } else {
                        attachmentFolder
                    }

                    // Navigate to the attachment folder
                    var currentFolder = vaultDoc
                    val folderParts = processedPath.split("/")
                    var validPath = true

                    for (part in folderParts) {
                        if (part.isEmpty()) continue
                        val folder = currentFolder?.findFile(part)
                        if (folder == null || !folder.isDirectory) {
                            validPath = false
                            break
                        }
                        currentFolder = folder
                    }

                    if (validPath) {
                        val file = currentFolder?.findFile(fileName)
                        if (file != null && file.isFile) {
                            Log.d(TAG, "Found file in attachment folder: $processedPath/$fileName")
                            return "$processedPath/$fileName"
                        }
                    }
                }
            }

            // If all else fails, just use the save location as a default
            Log.d(TAG, "Using default path as last resort: $saveLocation/$fileName")
            return "$saveLocation/$fileName"
        } catch (e: Exception) {
            Log.e(TAG, "Error getting relative path", e)
            return null
        }
    }

    private fun createCalendarViewUri(@Suppress("UNUSED_PARAMETER") eventDescription: String, eventStartTime: Long): String {
        // Create a URI that can be used to view this event in the calendar app
        // This is a placeholder - you'll need to implement the actual logic
        return "content://com.android.calendar/events/view?time=$eventStartTime"
    }
}
