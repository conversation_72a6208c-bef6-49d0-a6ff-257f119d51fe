package com.devindie.keepsidian.analytics

import android.content.Context
import android.os.Bundle
import com.google.firebase.analytics.FirebaseAnalytics

/**
 * Helper class for tracking analytics events throughout the app
 */
object AnalyticsHelper {
    private lateinit var firebaseAnalytics: FirebaseAnalytics

    fun init(context: Context) {
        firebaseAnalytics = FirebaseAnalytics.getInstance(context)
    }

    // Feature Usage Events
    fun trackOCRUsage(success: Boolean, processingTimeMs: Long) {
        val bundle = Bundle().apply {
            putBoolean("success", success)
            putLong("processing_time_ms", processingTimeMs)
        }
        firebaseAnalytics.logEvent("ocr_image_processing", bundle)
    }

    fun trackSpeechToText(success: Boolean, processingTimeMs: Long, offline: Boolean) {
        val bundle = Bundle().apply {
            putBoolean("success", success)
            putLong("processing_time_ms", processingTimeMs)
            putBoolean("offline_mode", offline)
        }
        firebaseAnalytics.logEvent("speech_to_text", bundle)
    }

    fun trackYouTubeExtraction(success: Boolean) {
        val bundle = Bundle().apply {
            putBoolean("success", success)
        }
        firebaseAnalytics.logEvent("youtube_extraction", bundle)
    }

    fun trackWidgetInteraction(widgetType: String, action: String) {
        val bundle = Bundle().apply {
            putString("widget_type", widgetType)
            putString("action", action)
        }
        firebaseAnalytics.logEvent("widget_interaction", bundle)
    }

    fun trackCalendarEventCreation(success: Boolean, addToFutureDate: Boolean) {
        val bundle = Bundle().apply {
            putBoolean("success", success)
            putBoolean("add_to_future_date", addToFutureDate)
        }
        firebaseAnalytics.logEvent("calendar_event_creation", bundle)
    }

    // Content Creation Events
    fun trackNoteCreation(type: String, hasAttachments: Boolean) {
        val bundle = Bundle().apply {
            putString("note_type", type)
            putBoolean("has_attachments", hasAttachments)
        }
        firebaseAnalytics.logEvent("note_creation", bundle)
    }

    fun trackTaskCreation(isCalendarEvent: Boolean) {
        val bundle = Bundle().apply {
            putBoolean("is_calendar_event", isCalendarEvent)
        }
        firebaseAnalytics.logEvent("task_creation", bundle)
    }

    // Obsidian Integration Events
    fun trackVaultConfiguration(success: Boolean) {
        val bundle = Bundle().apply {
            putBoolean("success", success)
        }
        firebaseAnalytics.logEvent("vault_configuration", bundle)
    }

    fun trackDailyNoteAccess(success: Boolean) {
        val bundle = Bundle().apply {
            putBoolean("success", success)
        }
        firebaseAnalytics.logEvent("daily_note_access", bundle)
    }

    // Error Tracking
    fun trackError(feature: String, errorMessage: String) {
        val bundle = Bundle().apply {
            putString("feature", feature)
            putString("error_message", errorMessage)
        }
        firebaseAnalytics.logEvent("app_error", bundle)
    }

    // Performance Metrics
    fun trackAppStartup(timeMs: Long) {
        val bundle = Bundle().apply {
            putLong("time_ms", timeMs)
        }
        firebaseAnalytics.logEvent("app_startup", bundle)
    }

    // User Preferences
    fun trackPreferenceChange(preference: String, value: String) {
        val bundle = Bundle().apply {
            putString("preference", preference)
            putString("value", value)
        }
        firebaseAnalytics.logEvent("preference_change", bundle)
    }
}