package com.devindie.keepsidian

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.DocumentsContract
import android.util.Log
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.net.toUri
import androidx.documentfile.provider.DocumentFile
import com.devindie.keepsidian.analytics.AnalyticsHelper
import java.time.LocalDate
import java.time.format.DateTimeFormatter

class OpenObsidianActivity : AppCompatActivity() {
    companion object {
        private const val TAG = "OpenObsidianActivity"

        // Constants for intent extras
        const val EXTRA_VAULT_NAME = "vault_name"
        const val EXTRA_FILE_PATH = "file_path"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val appDataStore = AppModule.provideAppDataStoreRepository(applicationContext)
        val vaultUriString = appDataStore.getStringValue(Constants.Preferences.OBSIDIAN_VAULT_URI, "")

        if (vaultUriString.isEmpty()) {
            Toast.makeText(this, "Obsidian vault not configured", Toast.LENGTH_SHORT).show()
            finish()
            return
        }

        try {
            // Get vault name from URI
            val vaultDoc = DocumentFile.fromTreeUri(this, vaultUriString.toUri())
            val vaultName = vaultDoc?.name ?: "vault"

            // Check if we have extras for vault name and file path
            val extraVaultName = intent.getStringExtra(EXTRA_VAULT_NAME)
            val extraFilePath = intent.getStringExtra(EXTRA_FILE_PATH)

            if (extraVaultName != null && extraFilePath != null) {
                // We have specific vault name and file path from extras
                Log.d(TAG, "Opening specific file from extras: vault=$extraVaultName, file=$extraFilePath")
                openInObsidian(extraVaultName, extraFilePath)
                return
            }

            // Check if we have a specific file URI to open
            val specificFileUri = intent.data
            if (specificFileUri != null) {
                // We have a specific file to open
                Log.d(TAG, "Opening specific file from URI: $specificFileUri")

                // Find the relative path from the vault root
                val relativePath = getRelativePathFromVault(vaultDoc, specificFileUri)
                if (relativePath != null) {
                    openInObsidian(vaultName, relativePath)
                    return
                }
            }

            // Default to opening today's daily note
            val today = LocalDate.now()
            val dailyNotesFormat = appDataStore.getStringValue(Constants.Preferences.OBSIDIAN_DAILY_NOTES_FORMAT, "yyyy-MM-dd")
            val formatter = DateTimeFormatter.ofPattern(dailyNotesFormat)
            val todayFileName = "${today.format(formatter)}"
            val dailyNotesFolder = appDataStore.getStringValue(Constants.Preferences.OBSIDIAN_DAILY_NOTES_FOLDER, "")

            // Create path with folder if needed
            val filePath = if (dailyNotesFolder.isEmpty()) todayFileName
            else "$dailyNotesFolder/$todayFileName"

            openInObsidian(vaultName, filePath)
            AnalyticsHelper.trackDailyNoteAccess(true)
        } catch (e: Exception) {
            Log.e(TAG, "Error opening Obsidian", e)
            Toast.makeText(this, "Error opening Obsidian: ${e.message}", Toast.LENGTH_SHORT).show()
            AnalyticsHelper.trackDailyNoteAccess(false)
        } finally {
            finish()
        }
    }

    private fun openInObsidian(vaultName: String, filePath: String) {
        // Create obsidian:// URI to open the file
        val obsidianUri = Uri.parse(
            "obsidian://open?vault=${Uri.encode(vaultName)}&file=${Uri.encode(filePath)}"
        )

        // Create and start the intent
        val intent = Intent(Intent.ACTION_VIEW, obsidianUri)
        startActivity(intent)
    }

    private fun getRelativePathFromVault(vaultDoc: DocumentFile?, fileUri: Uri): String? {
        if (vaultDoc == null) return null

        // Get the document file for the specific URI
        val docFile = DocumentFile.fromSingleUri(this, fileUri) ?: return null

        // Get the file name
        val fileName = docFile.name ?: return null

        // Try to find the file in the vault by traversing the directory structure
        return findFileInVault(vaultDoc, fileUri, fileName, "")
    }

    /**
     * Recursively traverses the vault to find a file matching the given URI
     * @param currentDir The current directory to search in
     * @param targetUri The URI of the file we're looking for
     * @param targetFileName The name of the file we're looking for (for optimization)
     * @param currentPath The current relative path from the vault root
     * @return The relative path from the vault root if found, null otherwise
     */
    private fun findFileInVault(
        currentDir: DocumentFile,
        targetUri: Uri,
        targetFileName: String,
        currentPath: String
    ): String? {
        Log.d(TAG, "Searching in directory: ${currentDir.name}, path: $currentPath")

        // List all files in the current directory
        val files = currentDir.listFiles()

        for (file in files) {
            // Skip hidden files/folders (those starting with .)
            if (file.name?.startsWith(".") == true) continue

            if (file.isDirectory) {
                // Recursively search in subdirectories
                val newPath = if (currentPath.isEmpty()) file.name else "$currentPath/${file.name}"
                val result = newPath?.let { findFileInVault(file, targetUri, targetFileName, it) }
                if (result != null) {
                    return result
                }
            } else if (file.isFile) {
                // Check if this file matches our target
                if (file.name == targetFileName) {
                    // Compare URIs to ensure it's the exact file we're looking for
                    if (areUrisEquivalent(file.uri, targetUri)) {
                        val path = if (currentPath.isEmpty()) file.name else "$currentPath/${file.name}"
                        Log.d(TAG, "Found file at path: $path")
                        return path
                    }
                }
            }
        }

        // File not found in this directory or its subdirectories
        return null
    }

    /**
     * Compares two URIs to check if they point to the same file
     * Different URI schemes might be used for the same file, so we need to compare carefully
     */
    private fun areUrisEquivalent(uri1: Uri, uri2: Uri): Boolean {
        // Direct comparison first
        if (uri1 == uri2) return true

        // If URIs are different, try to compare the document IDs
        // This is a more reliable way to check if two URIs point to the same file
        try {
            val docId1 = DocumentsContract.getDocumentId(uri1)
            val docId2 = DocumentsContract.getDocumentId(uri2)
            return docId1 == docId2
        } catch (e: Exception) {
            Log.e(TAG, "Error comparing URIs", e)
        }

        // If we can't compare document IDs, fall back to comparing last path segments
        val lastSegment1 = uri1.lastPathSegment
        val lastSegment2 = uri2.lastPathSegment
        return lastSegment1 != null && lastSegment1 == lastSegment2
    }
}
