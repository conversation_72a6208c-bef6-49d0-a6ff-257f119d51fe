package com.devindie.keepsidian

import com.devindie.keepsidian.entity.NoteEntity
import kotlinx.coroutines.flow.Flow

interface NoteRepository {

    fun getAllNotes(): Flow<List<NoteEntity>>
    fun getAllDeletedNotes(): Flow<List<NoteEntity>>
    fun getNotesByFolderId(folderId: Long?): Flow<List<NoteEntity>>
    fun getNotesByKeyWord(keyWord: String): Flow<List<NoteEntity>>

    suspend fun getNoteById(id: Long): NoteEntity?

    suspend fun insertNote(note: NoteEntity): Long

    suspend fun deleteNote(noteEntity: NoteEntity)

    suspend fun deleteNotesByFolderId(folderId: Long?)

    suspend fun updateNote(note: NoteEntity)

}