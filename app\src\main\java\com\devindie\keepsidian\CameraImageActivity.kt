package com.devindie.keepsidian

import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.MediaStore
import android.util.Log
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.FileProvider
import com.devindie.keepsidian.services.ImageService
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * Activity for taking a picture with the camera and saving it to Obsidian vault
 * without OCR processing
 */
class CameraImageActivity : ComponentActivity() {

    companion object {
        private const val TAG = "CameraImageActivity"
    }

    private lateinit var cameraPermissionLauncher: ActivityResultLauncher<String>
    private lateinit var takePictureLauncher: ActivityResultLauncher<Uri>
    private lateinit var tempImageUri: Uri

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Register take picture launcher
        takePictureLauncher = registerForActivityResult(
            ActivityResultContracts.TakePicture()
        ) { success ->
            if (success) {
                // Image captured successfully
                processImage(tempImageUri)
            } else {
                // Image capture failed or was cancelled
                Toast.makeText(
                    this,
                    getString(R.string.failed_to_capture_image),
                    Toast.LENGTH_SHORT
                ).show()
                finish()
            }
        }

        // Register camera permission launcher with callback to launch camera when granted
        cameraPermissionLauncher = CameraPermissionHelper.registerPermissionLauncher(
            this,
            onPermissionGranted = { launchCamera() }
        )

        // Check and request camera permission if needed
        if (!CameraPermissionHelper.hasCameraPermission(this)) {
            CameraPermissionHelper.requestCameraPermissionIfNeeded(this, cameraPermissionLauncher)
        } else {
            // Permission already granted, launch camera
            launchCamera()
        }
    }

    private fun launchCamera() {
        try {
            // Create a temporary file to store the image
            val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val imageFileName = "JPEG_${timeStamp}_"
            val storageDir = getExternalFilesDir(null)
            val imageFile = File.createTempFile(
                imageFileName,
                ".jpg",
                storageDir
            )

            // Get URI for the file using FileProvider
            tempImageUri = FileProvider.getUriForFile(
                this,
                "${applicationContext.packageName}.fileprovider",
                imageFile
            )

            // Create intent to specify back camera
            val takePictureIntent = Intent(MediaStore.ACTION_IMAGE_CAPTURE)
            takePictureIntent.putExtra(MediaStore.EXTRA_OUTPUT, tempImageUri)

            // Specify to use the back camera
            takePictureIntent.putExtra("android.intent.extras.CAMERA_FACING", android.hardware.Camera.CameraInfo.CAMERA_FACING_BACK)
            // For newer Android versions
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1) {
                takePictureIntent.putExtra("android.intent.extras.LENS_FACING_FRONT", 0)
                takePictureIntent.putExtra("android.intent.extra.USE_FRONT_CAMERA", false)
            }

            // Launch the camera
            takePictureLauncher.launch(tempImageUri)
        } catch (e: Exception) {
            Log.e(TAG, "Error launching camera", e)
            Toast.makeText(
                this,
                getString(R.string.failed_to_launch_camera),
                Toast.LENGTH_SHORT
            ).show()
            finish()
        }
    }

    private fun processImage(imageUri: Uri) {
        // Check if the Obsidian vault is configured
        val appDataStore = AppModule.provideAppDataStoreRepository(applicationContext)
        val vaultUriString = appDataStore.getStringValue(Constants.Preferences.OBSIDIAN_VAULT_URI, "")
        val attachmentFolderPath = appDataStore.getStringValue(Constants.Preferences.OBSIDIAN_ATTACHMENT_FOLDER, "")

        if (vaultUriString.isEmpty()) {
            Toast.makeText(
                this,
                getString(R.string.vault_not_configured_open_app),
                Toast.LENGTH_LONG
            ).show()
            finish()
            return
        }

        // Start the Image service
        ImageService.startService(
            this,
            imageUri.toString(),
            vaultUriString,
            attachmentFolderPath
        )

        Toast.makeText(
            this,
            getString(R.string.saving_image),
            Toast.LENGTH_SHORT
        ).show()

        finish()
    }
}
