package com.devindie.keepsidian.services

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Intent
import android.os.Build
import android.os.PowerManager
import android.util.Log
import androidx.core.app.NotificationCompat
import com.devindie.keepsidian.R
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel

abstract class BaseProcessingService : Service() {
    protected val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    protected var wakeLock: PowerManager.WakeLock? = null

    companion object {
        const val NOTIFICATION_CHANNEL_ID = "keepsidian_processing_channel"
        const val WAKE_LOCK_TIMEOUT = 10 * 60 * 1000L // 10 minutes max
    }

    abstract val serviceName: String
    abstract val notificationId: Int

    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()

        // Set process priority to foreground
        setForegroundServicePriority()

        // Acquire wake lock with longer timeout
        val powerManager = getSystemService(POWER_SERVICE) as PowerManager
        wakeLock = powerManager.newWakeLock(
            PowerManager.PARTIAL_WAKE_LOCK,
            "keepsidian:${serviceName}WakeLock"
        )
        wakeLock?.acquire(WAKE_LOCK_TIMEOUT) // Extended timeout
    }

    /**
     * Sets the process priority to foreground to reduce the chance of the system killing it
     */
    private fun setForegroundServicePriority() {
        try {
            // Set thread priority
            android.os.Process.setThreadPriority(
                android.os.Process.THREAD_PRIORITY_FOREGROUND
            )
        } catch (e: Exception) {
            Log.e(serviceName, "Failed to set process priority", e)
        }
    }

    protected fun createNotification(title: String, message: String) =
        NotificationCompat.Builder(this, NOTIFICATION_CHANNEL_ID)
            .setContentTitle(title)
            .setContentText(message)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_HIGH) // Higher priority notification
            .build()

    protected fun updateNotification(title: String, message: String) {
        val notificationManager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(notificationId, createNotification(title, message))
        
        // Log updates for debugging
        Log.d(serviceName, "Notification updated: $title - $message")
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                NOTIFICATION_CHANNEL_ID,
                "keepsidian Processing",
                NotificationManager.IMPORTANCE_HIGH // Higher importance channel
            )
            channel.setShowBadge(true)
            channel.enableVibration(false) // Disable vibration to reduce battery usage
            channel.enableLights(false)    // Disable lights to reduce battery usage
            
            val notificationManager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    override fun onStartCommand(intent: Intent, flags: Int, startId: Int): Int {
        // Return START_REDELIVER_INTENT to ensure the service restarts if killed
        return START_REDELIVER_INTENT
    }

    override fun onDestroy() {
        super.onDestroy()
        if (wakeLock?.isHeld == true) {
            try {
                wakeLock?.release()
            } catch (e: Exception) {
                Log.e(serviceName, "Error releasing wake lock", e)
            }
        }
        serviceScope.cancel()
    }

    override fun onBind(intent: android.content.Intent?): android.os.IBinder? {
        // This is a started service, not a bound service, so return null
        return null
    }
}
