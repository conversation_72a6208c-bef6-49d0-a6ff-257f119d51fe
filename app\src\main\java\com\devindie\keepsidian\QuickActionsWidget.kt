package com.devindie.keepsidian

import android.content.Context
import android.content.Intent
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.unit.dp
import androidx.glance.GlanceId
import androidx.glance.GlanceModifier
import androidx.glance.GlanceTheme
import androidx.glance.ImageProvider
import androidx.glance.LocalSize
import androidx.glance.action.ActionParameters
import androidx.glance.appwidget.GlanceAppWidget
import androidx.glance.appwidget.SizeMode
import androidx.glance.appwidget.action.ActionCallback
import androidx.glance.appwidget.action.actionRunCallback
import androidx.glance.appwidget.components.CircleIconButton
import androidx.glance.appwidget.components.Scaffold
import androidx.glance.appwidget.provideContent
import androidx.glance.layout.Alignment
import androidx.glance.layout.Row
import androidx.glance.layout.Spacer
import androidx.glance.layout.fillMaxWidth
import androidx.glance.layout.padding
import androidx.glance.layout.size

// Define transparent color providers for widget background

class QuickActionsWidget : GlanceAppWidget() {

    override val sizeMode = SizeMode.Exact

    override suspend fun provideGlance(context: Context, id: GlanceId) {
        val widgetPreferences =
            AppModule.provideWidgetDataStoreRepository(context.applicationContext)
        AppModule.provideAppDataStoreRepository(context.applicationContext)

        provideContent {
            val backgroundColor by widgetPreferences.intFlow(Constants.Widget.WIDGET_BACKGROUND_COLOR)
                .collectAsState(initial = 1)

            GlanceTheme(
                colors = if (backgroundColor == 1) GlanceTheme.colors else transparentColorProviders
            ) {
                Content(backgroundColor = backgroundColor)
            }
        }
    }

    @Composable
    private fun Content(backgroundColor: Int = 1) {
        LocalSize.current

        Scaffold(
            backgroundColor = GlanceTheme.colors.background
        ) {
            Row(
                modifier = GlanceModifier.fillMaxWidth()
                    .padding(16.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // Voice recording button
                CircleIconButton(
                    imageProvider = ImageProvider(R.drawable.mic),
                    contentDescription = "Voice Recording",
                    onClick = actionRunCallback<VoiceRecordingAction>()
                )

                Spacer(modifier = GlanceModifier.size(24.dp))

                // OCR Camera button
                CircleIconButton(
                    imageProvider = ImageProvider(R.drawable.ocr_camera),
                    contentDescription = "OCR Image",
                    onClick = actionRunCallback<CameraAction>()
                )

                Spacer(modifier = GlanceModifier.size(24.dp))

                // Regular Camera button
                CircleIconButton(
                    imageProvider = ImageProvider(R.drawable.camera),
                    contentDescription = "Take Picture",
                    onClick = actionRunCallback<CameraImageAction>()
                )

                Spacer(modifier = GlanceModifier.size(24.dp))

                // Calendar button
                CircleIconButton(
                    imageProvider = ImageProvider(R.drawable.add_task),
                    contentDescription = "Add Calendar Event",
                    onClick = actionRunCallback<CalendarAction>()
                )
            }
        }
    }
}

class CalendarAction : ActionCallback {
    override suspend fun onAction(
        context: Context, glanceId: GlanceId, parameters: ActionParameters
    ) {
        val intent = Intent(context, CalendarEventActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        context.startActivity(intent)
    }
}
