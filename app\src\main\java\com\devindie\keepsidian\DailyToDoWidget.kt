package com.devindie.keepsidian

import android.content.Context
import android.content.Intent
import android.util.Log
import android.widget.Toast
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.net.toUri
import androidx.documentfile.provider.DocumentFile
import com.devindie.keepsidian.analytics.AnalyticsHelper
import androidx.glance.GlanceId
import androidx.glance.GlanceModifier
import androidx.glance.GlanceTheme
import androidx.glance.ImageProvider
import androidx.glance.LocalContext
import androidx.glance.LocalSize
import androidx.glance.action.ActionParameters
import androidx.glance.action.actionParametersOf
import androidx.glance.action.actionStartActivity
import androidx.glance.action.clickable
import androidx.glance.appwidget.CheckBox
import androidx.glance.appwidget.CircularProgressIndicator
import androidx.glance.appwidget.GlanceAppWidget
import androidx.glance.appwidget.SizeMode
import androidx.glance.appwidget.action.ActionCallback
import androidx.glance.appwidget.action.actionRunCallback
import androidx.glance.appwidget.components.CircleIconButton
import androidx.glance.appwidget.components.Scaffold
import androidx.glance.appwidget.lazy.LazyColumn
import androidx.glance.appwidget.lazy.items
import androidx.glance.appwidget.provideContent
import androidx.glance.background
import androidx.glance.color.ColorProvider
import androidx.glance.layout.Alignment
import androidx.glance.layout.Column
import androidx.glance.layout.Row
import androidx.glance.layout.Spacer
import androidx.glance.layout.fillMaxWidth
import androidx.glance.layout.height
import androidx.glance.layout.padding
import androidx.glance.layout.size
import androidx.glance.text.Text
import androidx.glance.text.TextAlign
import androidx.glance.text.TextDecoration
import androidx.glance.text.TextStyle
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.time.LocalDate
import java.time.format.DateTimeFormatter

class DailyToDoWidget : GlanceAppWidget() {

    override val sizeMode = SizeMode.Exact

    override suspend fun provideGlance(context: Context, id: GlanceId) {
        val widgetPreferences =
            AppModule.provideWidgetDataStoreRepository(context.applicationContext)
        val appDataStore = AppModule.provideAppDataStoreRepository(context.applicationContext)

        // Get vault URI, daily notes folder, and format from preferences
        val vaultUriString =
            appDataStore.getStringValue(Constants.Preferences.OBSIDIAN_VAULT_URI, "")
        val dailyNotesFolder =
            appDataStore.getStringValue(Constants.Preferences.OBSIDIAN_DAILY_NOTES_FOLDER, "")
        val dailyNotesFormat =
            appDataStore.getStringValue(Constants.Preferences.OBSIDIAN_DAILY_NOTES_FORMAT, "yyyy-MM-dd")

        // Create config
        val config = WidgetConfig(vaultUriString, dailyNotesFolder, dailyNotesFormat)

        // Load todos from file and update repo
        TodoRepo.loadTodosFromFile(context, config)

        provideContent {
            val textSize by widgetPreferences.intFlow(Constants.Widget.WIDGET_TEXT_SIZE)
                .collectAsState(initial = 1)
            val backgroundColor by widgetPreferences.intFlow(Constants.Widget.WIDGET_BACKGROUND_COLOR)
                .collectAsState(initial = 1)
            val tasks by TodoRepo.currentTodos.collectAsState()
            val isLoading by TodoRepo.isLoading.collectAsState()

            GlanceTheme(
                colors = if (backgroundColor == 1) GlanceTheme.colors else transparentColorProviders
            ) {
                Content(
                    textSize = textSize,
                    backgroundColor = backgroundColor,
                    tasks = tasks,
                    isLoading = isLoading
                )
            }
        }
    }

    private fun cleanMarkdown(text: String): String {
        // First, handle markdown links - extract just the link text
        var cleanedText = text

        // Extract text from markdown links [text](url)
        val linkPattern = Regex("\\[([^\\]]+)\\]\\([^)]+\\)")
        val linkMatcher = linkPattern.findAll(cleanedText)
        for (match in linkMatcher) {
            val fullMatch = match.value
            val linkText = match.groupValues[1]
            cleanedText = cleanedText.replace(fullMatch, linkText)
        }

        // Remove all other markdown syntax
        return cleanedText.replace("~~", "")
            .replace("**", "")
            .replace("__", "")
            .replace("*", "")
            .replace("_", "")
            .replace("==", "")
            .replace("++", "")
            .replace("<strong>", "")
            .replace("</strong>", "")
            .replace("<em>", "")
            .replace("</em>", "")
            .replace("<ins>", "")
            .replace("</ins>", "")
            .replace("<mark>", "")
            .replace("</mark>", "")
            .replace("`", "")
    }

    private fun parseMarkdownToTextStyle(text: String, baseStyle: TextStyle): TextStyle {
        // Create a new TextStyle with appropriate styling based on markdown syntax
        return baseStyle.copy(
            textDecoration = when {
                text.contains("~~") -> TextDecoration.LineThrough
                text.contains("++") || text.contains("<ins>") -> TextDecoration.Underline
                else -> TextDecoration.None
            },
            fontWeight = if (text.contains("**") || text.contains("__") || text.contains("<strong>"))
                androidx.glance.text.FontWeight.Bold
            else
                androidx.glance.text.FontWeight.Normal,
            fontStyle = if (text.contains("*") || text.contains("_") || text.contains("<em>"))
                androidx.glance.text.FontStyle.Italic
            else
                androidx.glance.text.FontStyle.Normal,
            color = if (text.contains("==") || text.contains("<mark>"))
                ColorProvider(
                    Color(1.0f, 0.843f, 0.0f), // Gold for day (#FFD700)
                    Color(1.0f, 0.647f, 0.0f)  // Orange for night (#FFA500)
                )
            else
                baseStyle.color
        )
    }

    @Composable
    private fun Content(
        textSize: Int = 1,
        @Suppress("UNUSED_PARAMETER") backgroundColor: Int = 1,
        tasks: List<TodoItem> = emptyList(),
        isLoading: Boolean = false
    ) {
        // Get callouts from TodoRepo
        val callouts by TodoRepo.currentCallouts.collectAsState()
        val contentSize by androidx.compose.runtime.remember(textSize) {
            androidx.compose.runtime.mutableStateOf(
                when (textSize) {
                    0 -> 12.sp
                    1 -> 14.sp
                    2 -> 16.sp
                    else -> 14.sp
                }
            )
        }

        val size = LocalSize.current
        val context = LocalContext.current
        Scaffold(
            titleBar = {
                Row(
                    modifier = GlanceModifier.fillMaxWidth()
                        .padding(bottom = 8.dp, top = if (size.width > 250.dp) 16.dp else 8.dp)
                        .padding(horizontal = if (size.width > 250.dp) 16.dp else 8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = LocalDate.now()
                            .format(DateTimeFormatter.ofPattern(context.getString(R.string.date_format))),
                        style = TextStyle(
                            color = GlanceTheme.colors.onSurface,
                            fontSize = 16.sp,
                            fontWeight = androidx.glance.text.FontWeight.Bold
                        )
                    )

                    Spacer(modifier = GlanceModifier.defaultWeight())

                    // Voice recording button
                    CircleIconButton(
                        imageProvider = ImageProvider(R.drawable.mic),
                        contentDescription = context.getString(R.string.voice_recording_content_description),
                        onClick = actionRunCallback<VoiceRecordingAction>()
                    )

                    Spacer(modifier = GlanceModifier.size(8.dp))

                    // OCR Camera button
                    CircleIconButton(
                        imageProvider = ImageProvider(R.drawable.ocr_camera),
                        contentDescription = context.getString(R.string.camera_content_description),
                        onClick = actionRunCallback<CameraAction>()
                    )

                    Spacer(modifier = GlanceModifier.size(8.dp))

                    // Regular Camera button
                    CircleIconButton(
                        imageProvider = ImageProvider(R.drawable.camera),
                        contentDescription = "Take Picture",
                        onClick = actionRunCallback<CameraImageAction>()
                    )

                    Spacer(modifier = GlanceModifier.size(8.dp))

                    // Calendar button
                    CircleIconButton(
                        imageProvider = ImageProvider(R.drawable.add_task),
                        contentDescription = context.getString(R.string.add_calendar_event),
                        onClick = actionRunCallback<CalendarAction>()
                    )
                }
            }, horizontalPadding = if (size.width > 250.dp) 16.dp else 4.dp
        ) {
            // Show loading indicator at the top if loading
            if (isLoading) {
                // Show loading animation
                Column(
                    modifier = GlanceModifier.fillMaxWidth().padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    CircularProgressIndicator(
                        color = ColorProvider(Color.Black, Color.White)
                    )
                }
                return@Scaffold // Exit early to ensure only the loading indicator is shown
            }

            // Always show the LazyColumn, even when loading
            // This allows us to display callouts even if tasks are still loading or empty
            LazyColumn {
                // Display tasks section if there are tasks
                if (tasks.isNotEmpty()) {
                    item {
                        Text(
                            text = context.getString(R.string.tasks),
                            style = TextStyle(
                                color = GlanceTheme.colors.onSurface,
                                fontSize = 16.sp,
                                fontWeight = androidx.glance.text.FontWeight.Bold
                            ),
                            modifier = GlanceModifier.padding(horizontal = 10.dp, vertical = 8.dp)
                        )
                    }

                    items(tasks) { task ->
                        Column {
                            // Common modifier for both task types
                            val taskModifier = GlanceModifier.fillMaxWidth()
                                .padding(horizontal = 10.dp, vertical = 2.dp)
                                .background(GlanceTheme.colors.secondaryContainer)
                                .appWidgetInnerCornerRadius().padding(
                                    start = 8.dp + (task.indentLevel * 16).dp,
                                    end = 8.dp,
                                    top = 8.dp,
                                    bottom = 8.dp
                                )

                            // Common text style for both task types
                            val baseTextStyle = TextStyle(
                                color = if (task.isCompleted) GlanceTheme.colors.onSurfaceVariant
                                else GlanceTheme.colors.onSurface,
                                fontSize = contentSize,
                                textDecoration = if (task.isCompleted) TextDecoration.LineThrough
                                else TextDecoration.None
                            )

                            // Render differently based on task type
                            if (task.isCalendarEvent) {
                                // Calendar event task - use clickable text
                                Text(
                                    text = cleanMarkdown(task.text),
                                    style = parseMarkdownToTextStyle(task.text, baseTextStyle),
                                    modifier = taskModifier.clickable(
                                        actionRunCallback<ToggleTaskAction>(
                                            parameters = actionParametersOf(taskTextKey to task.originalLine)
                                        )
                                    )
                                )
                            } else {
                                // Regular task - use checkbox
                                CheckBox(
                                    checked = task.isCompleted,
                                    onCheckedChange = actionRunCallback<ToggleTaskAction>(
                                        parameters = actionParametersOf(taskTextKey to task.originalLine)
                                    ),
                                    text = cleanMarkdown(task.text),
                                    style = parseMarkdownToTextStyle(task.text, baseTextStyle),
                                    modifier = taskModifier
                                )
                            }
                            Spacer(modifier = GlanceModifier.height(4.dp))
                        }
                    }

                    // Add spacing between sections if we have both tasks and callouts
                    if (callouts.isNotEmpty()) {
                        item {
                            Spacer(modifier = GlanceModifier.height(16.dp))
                        }
                    }
                }

                // Display callouts section if there are callouts
                if (callouts.isNotEmpty()) {
                    item {
                        Text(
                            text = context.getString(R.string.callouts),
                            style = TextStyle(
                                color = GlanceTheme.colors.onSurface,
                                fontSize = 16.sp,
                                fontWeight = androidx.glance.text.FontWeight.Bold
                            ),
                            modifier = GlanceModifier.padding(horizontal = 10.dp, vertical = 8.dp)
                        )
                    }

                    items(callouts) { callout ->
                        Column {
                            // Callout item
                            Row(
                                modifier = GlanceModifier.fillMaxWidth()
                                    .padding(horizontal = 10.dp, vertical = 2.dp)
                                    .background(GlanceTheme.colors.primaryContainer)
                                    .appWidgetInnerCornerRadius()
                                    .padding(8.dp)
                                    .clickable(
                                        actionRunCallback<CalloutAction>(
                                            parameters = actionParametersOf(calloutBlockKey to callout.originalBlock)
                                        )
                                    ),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                // Emoji
                                Text(
                                    text = callout.calloutType,
                                    style = TextStyle(
                                        fontSize = contentSize
                                    ),
                                    modifier = GlanceModifier.padding(end = 8.dp)
                                )

                                // Title
                                Text(
                                    text = callout.title,
                                    style = TextStyle(
                                        color = GlanceTheme.colors.onPrimaryContainer,
                                        fontSize = contentSize
                                    )
                                )
                            }
                            Spacer(modifier = GlanceModifier.height(4.dp))
                        }
                    }

                    // Add spacing after callouts section
                    item {
                        Spacer(modifier = GlanceModifier.height(16.dp))
                    }
                }

                // Show a message if both tasks and callouts are empty
                if (tasks.isEmpty() && callouts.isEmpty() && !isLoading) {
                    item {
                        Column(
                            modifier = GlanceModifier.fillMaxWidth().padding(16.dp),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = context.getString(R.string.no_tasks_for_today),
                                style = TextStyle(
                                    color = GlanceTheme.colors.onSurfaceVariant,
                                    fontSize = contentSize
                                )
                            )
                        }
                    }
                }

                // Open in Obsidian button and Refresh button (always shown)
                item {
                    Row(
                        modifier = GlanceModifier.fillMaxWidth()
                            .padding(top = 8.dp, bottom = 24.dp, start = 16.dp, end = 16.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        // Open in Obsidian button
                        Text(
                            modifier = GlanceModifier
                                .defaultWeight()
                                .clickable(actionStartActivity<OpenObsidianActivity>()),
                            text = "Open in Obsidian",
                            style = TextStyle(
                                color = GlanceTheme.colors.primary,
                                textAlign = TextAlign.Center,
                                fontSize = 14.sp
                            ),
                        )

                        // Refresh button
                        CircleIconButton(
                            imageProvider = ImageProvider(R.drawable.sync),
                            contentDescription = "Refresh",
                            onClick = actionRunCallback<RefreshToDoAction>()
                        )
                    }
                }
            }
        }
    }
}

private val taskTextKey = ActionParameters.Key<String>("task_text")
private val calloutBlockKey = ActionParameters.Key<String>("callout_block")

class ToggleTaskAction : ActionCallback {
    override suspend fun onAction(
        context: Context, glanceId: GlanceId, parameters: ActionParameters
    ) {
        val taskText = parameters[taskTextKey] ?: return
        val appDataStore = AppModule.provideAppDataStoreRepository(context.applicationContext)

        val vaultUriString =
            appDataStore.getStringValue(Constants.Preferences.OBSIDIAN_VAULT_URI, "")
        val dailyNotesFolder =
            appDataStore.getStringValue(Constants.Preferences.OBSIDIAN_DAILY_NOTES_FOLDER, "")
        val dailyNotesFormat =
            appDataStore.getStringValue(Constants.Preferences.OBSIDIAN_DAILY_NOTES_FORMAT, "yyyy-MM-dd")
        val config = WidgetConfig(vaultUriString, dailyNotesFolder, dailyNotesFormat)

        // First, check if this is a calendar event task
        // If it is, open the calendar app instead of toggling the task
        if (TodoRepo.handleTaskClick(context, taskText)) {
            // The click was handled as a calendar event, no need to update the task state
            return
        }

        // Not a calendar event, proceed with the normal toggle behavior
        // Update UI state immediately
        TodoRepo.toggleTodoState(taskText)
        DailyToDoWidget().update(context, glanceId)

        // Update file in background
        if (!config.isConfigured()) return

        withContext(Dispatchers.IO) {
            try {
                val vaultUri = config.vaultUriString.toUri()
                val vaultDoc = DocumentFile.fromTreeUri(context, vaultUri) ?: return@withContext

                // Navigate to daily notes folder
                val dailyNotesDir = if (dailyNotesFolder.isEmpty()) {
                    vaultDoc
                } else {
                    val folders = dailyNotesFolder.split("/")
                    var currentDir = vaultDoc

                    for (folder in folders) {
                        if (folder.isEmpty()) continue
                        currentDir = currentDir.findFile(folder) ?: return@withContext
                    }
                    currentDir
                }

                // Get today's date using the configured format
                val today = LocalDate.now()
                val formatter = DateTimeFormatter.ofPattern(config.dailyNotesFormat)
                val todayFileName = "${today.format(formatter)}.md"

                // Find today's note file
                val todayFile = dailyNotesDir.findFile(todayFileName) ?: return@withContext
                if (todayFile.canRead() && todayFile.canWrite()) {
                    // Read the file content
                    val inputStream = context.contentResolver.openInputStream(todayFile.uri)
                    val content = inputStream?.bufferedReader()?.use { it.readText() } ?: ""
                    inputStream?.close()

                    // Toggle the task state
                    val isCompleted = taskText.startsWith("- [x]")
                    val newTaskText = if (isCompleted) {
                        taskText.replace("- [x]", "- [ ]")
                    } else {
                        taskText.replace("- [ ]", "- [x]")
                    }

                    val newContent = content.replace(taskText, newTaskText)

                    // Write back to file
                    val outputStream = context.contentResolver.openOutputStream(todayFile.uri)
                    outputStream?.use { it.write(newContent.toByteArray()) }
                    outputStream?.close()

                    // Update widget
                    DailyToDoWidget().update(context, glanceId)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        AnalyticsHelper.trackWidgetInteraction("daily_todo", "toggle_task")
    }
}

class RefreshToDoAction : ActionCallback {
    override suspend fun onAction(
        context: Context, glanceId: GlanceId, parameters: ActionParameters
    ) {
        val appDataStore = AppModule.provideAppDataStoreRepository(context.applicationContext)
        val vaultUriString =
            appDataStore.getStringValue(Constants.Preferences.OBSIDIAN_VAULT_URI, "")
        val dailyNotesFolder =
            appDataStore.getStringValue(Constants.Preferences.OBSIDIAN_DAILY_NOTES_FOLDER, "")
        val dailyNotesFormat =
            appDataStore.getStringValue(Constants.Preferences.OBSIDIAN_DAILY_NOTES_FORMAT, "yyyy-MM-dd")
        val config = WidgetConfig(vaultUriString, dailyNotesFolder, dailyNotesFormat)

        // Set loading state to true and update widget to show loading animation
        TodoRepo._isLoading.value = true
        DailyToDoWidget().update(context, glanceId)

        // Load todos from file and update repo
        TodoRepo.loadTodosFromFile(context, config)

        // Update widget UI
        DailyToDoWidget().update(context, glanceId)
        AnalyticsHelper.trackWidgetInteraction("daily_todo", "refresh")
    }
}

class CameraAction : ActionCallback {
    override suspend fun onAction(
        context: Context, glanceId: GlanceId, parameters: ActionParameters
    ) {
        // Check if OCR is enabled
        val appDataStore = AppModule.provideAppDataStoreRepository(context.applicationContext)
        val isOCREnabled =
            appDataStore.getBooleanValue(Constants.Preferences.ENABLE_OCR_IMAGES, false)

        if (!isOCREnabled) {
            // Enable OCR programmatically
            appDataStore.putBoolean(Constants.Preferences.ENABLE_OCR_IMAGES, true)

            // Open the app to let the user know and show toast in MainActivity
            val intent = Intent(context, MainActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            // Add extra flag to indicate we need to show OCR toast
            intent.putExtra(Constants.EXTRA_SHOW_OCR_ENABLED_TOAST, true)
            context.startActivity(intent)
            return
        }

        // Launch camera activity
        val intent = Intent(context, CameraActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        context.startActivity(intent)
        AnalyticsHelper.trackWidgetInteraction("daily_todo", "camera_ocr")
    }
}

class VoiceRecordingAction : ActionCallback {
    override suspend fun onAction(
        context: Context, glanceId: GlanceId, parameters: ActionParameters
    ) {
        // Check if Speech-To-Text is enabled
        val appDataStore = AppModule.provideAppDataStoreRepository(context.applicationContext)
        val isSpeechToTextEnabled =
            appDataStore.getBooleanValue(Constants.Preferences.ENABLE_SPEECH_TO_TEXT, false)

        if (!isSpeechToTextEnabled) {
            // Enable Speech-To-Text programmatically
            appDataStore.putBoolean(Constants.Preferences.ENABLE_SPEECH_TO_TEXT, true)

            // Open the app to let the user know and show toast in MainActivity
            val intent = Intent(context, MainActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            // Add extra flag to indicate we need to show Speech-To-Text toast
            intent.putExtra(Constants.EXTRA_SHOW_SPEECH_TO_TEXT_ENABLED_TOAST, true)
            context.startActivity(intent)
            return
        }

        // Launch speech recording activity
        val intent = Intent(context, SpeechRecordingActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        context.startActivity(intent)
        AnalyticsHelper.trackWidgetInteraction("daily_todo", "voice_recording")
    }
}

class CalloutAction : ActionCallback {
    private val TAG = "CalloutAction"

    override suspend fun onAction(
        context: Context, glanceId: GlanceId, parameters: ActionParameters
    ) {
        val calloutBlock = parameters[calloutBlockKey]

        if (calloutBlock == null) {
            Log.e(TAG, "Callout block parameter is null")
            return
        }

        Log.d(TAG, "Handling callout click with block: $calloutBlock")

        // Handle the callout click using TodoRepo
        val result = TodoRepo.handleCalloutClick(context, calloutBlock)

        if (!result) {
            Log.e(TAG, "Failed to handle callout click")
            // Show a toast to the user
            withContext(Dispatchers.Main) {
                Toast.makeText(context, "Failed to open link in Obsidian", Toast.LENGTH_SHORT).show()
            }
        }
    }
}
