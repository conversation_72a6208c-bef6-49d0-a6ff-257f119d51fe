package com.devindie.keepsidian.services

import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.core.net.toUri
import androidx.documentfile.provider.DocumentFile
import com.devindie.keepsidian.AppModule
import com.devindie.keepsidian.Constants
import com.devindie.keepsidian.ModelDownloadManager
import com.devindie.keepsidian.R
import com.devindie.keepsidian.analytics.AnalyticsHelper
import com.devindie.keepsidian.utils.WidgetUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import org.vosk.Model
import org.vosk.Recognizer
import java.io.File
import java.io.FileInputStream
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class VoskTranscriptionService : BaseProcessingService() {
    companion object {
        private const val TAG = "VoskTranscriptionSvc"
        private const val NOTIFICATION_ID = 1005

        const val EXTRA_AUDIO_PATH = "com.devindie.keepsidian.extra.AUDIO_PATH"
        const val EXTRA_VAULT_URI = "com.devindie.keepsidian.extra.VAULT_URI"
        const val EXTRA_ATTACHMENT_FOLDER = "com.devindie.keepsidian.extra.ATTACHMENT_FOLDER"
        const val EXTRA_MODEL_LANGUAGE = "com.devindie.keepsidian.extra.MODEL_LANGUAGE"

        // Default model URL for English
        const val DEFAULT_MODEL_URL =
            "https://alphacephei.com/vosk/models/vosk-model-small-en-us-0.15.zip"

        // Model file name
        const val MODEL_ZIP_FILENAME = "vosk_model.zip"
        const val MODEL_FOLDER_NAME = "vosk_model"

        fun startService(
            context: Context,
            audioPath: String,
            vaultUri: String,
            attachmentFolder: String,
            modelLanguage: String = "en-us"
        ) {
            val intent = Intent(context, VoskTranscriptionService::class.java).apply {
                putExtra(EXTRA_AUDIO_PATH, audioPath)
                putExtra(EXTRA_VAULT_URI, vaultUri)
                putExtra(EXTRA_ATTACHMENT_FOLDER, attachmentFolder)
                putExtra(EXTRA_MODEL_LANGUAGE, modelLanguage)
            }

            context.startForegroundService(intent)
        }
    }

    override val serviceName: String = TAG
    override val notificationId: Int = NOTIFICATION_ID

    override fun onStartCommand(intent: Intent, flags: Int, startId: Int): Int {
        val startTime = System.currentTimeMillis()
        val audioPath = intent.getStringExtra(EXTRA_AUDIO_PATH) ?: ""
        val vaultUriString = intent?.getStringExtra(EXTRA_VAULT_URI) ?: ""
        val attachmentFolderPath = intent?.getStringExtra(EXTRA_ATTACHMENT_FOLDER) ?: ""
        intent?.getStringExtra(EXTRA_MODEL_LANGUAGE) ?: "en-us"

        if (audioPath.isEmpty() || vaultUriString.isEmpty()) {
            stopSelf(startId)
            return super.onStartCommand(intent, flags, startId)
        }

        // Start as foreground service
        startForeground(
            notificationId, createNotification(
                "Processing Speech",
                "Preparing for offline transcription..."
            )
        )

        // Process in background
        serviceScope.launch {
            try {
                // Step 1: Check if model exists, download if needed
                updateNotification("Processing Speech", "Checking speech recognition model...")
                val modelDir = ensureModelExists()
                if (modelDir == null) {
                    updateNotification("Error", "Failed to prepare speech recognition model")
                    stopSelf(startId)
                    return@launch
                }

                // Step 2: Transcribe audio using Vosk
                updateNotification("Processing Speech", "Transcribing audio...")
                val transcriptionResult = transcribeAudio(audioPath, modelDir.absolutePath)
                if (transcriptionResult == null) {
                    updateNotification("Error", "Failed to transcribe audio")
                    stopSelf(startId)
                    return@launch
                }

                // Step 3: Format the transcription as markdown
                updateNotification("Processing Speech", "Formatting transcription...")
                val audioFileName = File(audioPath).name
                val markdownContent = formatAsMarkdown(transcriptionResult, audioFileName)

                // Step 4: Save the audio file to the vault
                updateNotification("Processing Speech", "Saving audio file...")
                val savedAudioUri = saveAudioToVault(
                    audioPath,
                    audioFileName,
                    vaultUriString,
                    attachmentFolderPath
                )
                if (savedAudioUri == null) {
                    updateNotification("Error", "Failed to save audio file")
                    stopSelf(startId)
                    return@launch
                }

                // Step 6: Save transcription to vault
                updateNotification("Processing Speech", "Saving transcription...")
                val fileUri = saveToObsidianVault(markdownContent, vaultUriString)

                if (fileUri != null) {
                    // Success - update notification
                    updateNotification(
                        "Speech Processed",
                        "Offline transcription saved to Obsidian"
                    )

                    // Add link to today's note
                    addLinkToTodayNote(fileUri, vaultUriString)

                    // Refresh widgets
                    WidgetUtils.refreshDailyTodoWidgets(this@VoskTranscriptionService)

                    // Show completion notification
                    showCompletionNotification(fileUri)
                } else {
                    updateNotification("Error", "Failed to save transcription")
                }

                // Stop service after a short delay
                Thread.sleep(1000)
                val processingTime = System.currentTimeMillis() - startTime
                AnalyticsHelper.trackSpeechToText(true, processingTime, true)
                stopSelf(startId)
            } catch (e: Exception) {
                Log.e(TAG, "Error processing speech", e)
                val processingTime = System.currentTimeMillis() - startTime
                AnalyticsHelper.trackSpeechToText(false, processingTime, true)
                AnalyticsHelper.trackError("offline_transcription", e.message ?: "Unknown error")
                updateNotification("Error", "Failed to process speech: ${e.message}")
                stopSelf(startId)
            }
        }

        // Use the parent implementation which returns START_REDELIVER_INTENT
        return super.onStartCommand(intent, flags, startId)
    }

    private suspend fun ensureModelExists(): File? = withContext(Dispatchers.IO) {
        try {
            val modelDir = File(applicationContext.filesDir, MODEL_FOLDER_NAME)

            // Log the model directory path for debugging
            Log.d(TAG, "Checking model directory at: ${modelDir.absolutePath}")

            // Check if model directory exists and has content
            if (ModelDownloadManager.isModelDownloaded(applicationContext)) {
                // Check if the model directory contains the expected model files
                val modelFiles = modelDir.listFiles()
                if (modelFiles != null) {
                    Log.d(TAG, "Model directory contains ${modelFiles.size} files/directories")

                    // Check for specific model files or directories that should be present
                    val hasModelFiles =
                        modelFiles.any { it.isDirectory && it.name == "am" || it.name == "conf" || it.name == "ivector" }

                    if (hasModelFiles) {
                        Log.d(TAG, "Model files found at ${modelDir.absolutePath}")
                        return@withContext modelDir
                    } else {
                        // The model directory exists but doesn't contain the expected model structure
                        Log.e(
                            TAG,
                            "Model directory exists but doesn't contain expected model files. Found: ${modelFiles.joinToString { it.name }}"
                        )
                    }
                } else {
                    Log.e(TAG, "Model directory exists but is empty or cannot be read")
                }
            } else {
                Log.e(TAG, "Model directory doesn't exist or is empty: ${modelDir.absolutePath}")
            }

            // Model doesn't exist or is invalid, show notification and return null
            // The model should have been downloaded when the user enabled the feature
            Log.e(
                TAG,
                "Valid model not found. It should have been downloaded when the feature was enabled."
            )
            return@withContext null
        } catch (e: Exception) {
            Log.e(TAG, "Error ensuring model exists", e)
            return@withContext null
        }
    }

    private suspend fun transcribeAudio(
        audioFilePath: String,
        modelPath: String
    ): TranscriptionResult? = withContext(Dispatchers.IO) {
        try {
            // Process the audio file (WAV format from WaveRecorder)
            val audioFile = File(audioFilePath)
            Log.d(TAG, "Audio file size: ${audioFile.length()} bytes")
            Log.d(TAG, "Audio file path: ${audioFile.absolutePath}")
            Log.d(TAG, "Audio file format: ${audioFile.extension}")

            // Check if the file exists and has content
            if (!audioFile.exists() || audioFile.length() == 0L) {
                Log.e(TAG, "Audio file does not exist or is empty: $audioFilePath")
                return@withContext null
            }

            // We'll use the audio file directly

            // Use Android's MediaExtractor to get audio info
            val extractor = android.media.MediaExtractor()
            extractor.setDataSource(audioFilePath)

            // Convert the audio file to WAV format using Android's MediaCodec
            try {
                // Find the audio track
                var audioTrackIndex = -1
                for (i in 0 until extractor.trackCount) {
                    val format = extractor.getTrackFormat(i)
                    val mime = format.getString(android.media.MediaFormat.KEY_MIME)
                    if (mime?.startsWith("audio/") == true) {
                        audioTrackIndex = i
                        break
                    }
                }

                if (audioTrackIndex < 0) {
                    Log.e(TAG, "No audio track found")
                    return@withContext null
                }

                // Select the audio track
                extractor.selectTrack(audioTrackIndex)

                // Get the audio format
                val format = extractor.getTrackFormat(audioTrackIndex)
                val mime = format.getString(android.media.MediaFormat.KEY_MIME)

                Log.d(TAG, "Audio format: $mime")

                // Skip WAV header creation - we'll use the audio file directly

                // Now use Vosk to transcribe the audio
                Log.d(TAG, "Initializing Vosk model from path: $modelPath")

                // Check if the model path exists and is a directory
                val modelPathFile = File(modelPath)
                if (!modelPathFile.exists() || !modelPathFile.isDirectory) {
                    Log.e(TAG, "Model path does not exist or is not a directory: $modelPath")
                    return@withContext null
                }

                // List the contents of the model directory for debugging
                val modelContents = modelPathFile.listFiles()
                if (modelContents != null) {
                    Log.d(
                        TAG,
                        "Model directory contains: ${modelContents.joinToString { it.name }}"
                    )
                } else {
                    Log.e(TAG, "Cannot list contents of model directory")
                    return@withContext null
                }

                try {
                    val model = Model(modelPath)
                    val recognizer = Recognizer(model, 16000f).apply {
                        setWords(true)
                    }
                    Log.d(TAG, "Vosk model and recognizer initialized successfully")

                    // Process the audio file
                    val words = mutableListOf<WordTimestamp>()

                    // For WAV files from WaveRecorder, we can process them directly with Vosk
                    // Read the WAV file
                    val audioFile = File(audioFilePath)
                    val fileInputStream = FileInputStream(audioFile)

                    // Skip the WAV header (44 bytes)
                    val wavHeader = ByteArray(44)
                    fileInputStream.read(wavHeader)

                    // Process the audio data in chunks
                    val buffer = ByteArray(4096)
                    var bytesRead: Int

                    while (fileInputStream.read(buffer).also { bytesRead = it } != -1) {
                        if (bytesRead > 0) {
                            // Feed the audio data to Vosk
                            if (recognizer.acceptWaveForm(buffer, bytesRead)) {
                                val result = JSONObject(recognizer.result)
                                processResult(result, words)
                            }
                        }
                    }

                    // Process final result
                    val finalResult = JSONObject(recognizer.finalResult)
                    processResult(finalResult, words)

                    // Clean up
                    fileInputStream.close()

                    // Get the full text
                    val fullText = if (words.isNotEmpty()) {
                        words.joinToString(" ") { it.word }
                    } else {
                        // If no words were recognized, try to get the text from the final result
                        val finalResult = JSONObject(recognizer.finalResult)
                        finalResult.optString("text", "No speech detected")
                    }

                    // Clean up
                    recognizer.close()
                    model.close()

                    return@withContext TranscriptionResult(fullText, words)
                } catch (e: Exception) {
                    Log.e(TAG, "Error initializing or using Vosk model", e)
                    return@withContext null
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error transcribing audio", e)
                return@withContext null
            } finally {
                extractor.release()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error transcribing audio", e)
            return@withContext null
        }
    }

    private fun processResult(result: JSONObject, words: MutableList<WordTimestamp>) {
        Log.e(TAG, "Processing result: $result")
        if (result.has("result")) {
            val resultArray = result.getJSONArray("result")
            for (i in 0 until resultArray.length()) {
                val wordObj = resultArray.getJSONObject(i)
                val word = wordObj.getString("word")
                val start = wordObj.getDouble("start").toFloat()
                val end = wordObj.getDouble("end").toFloat()
                words.add(WordTimestamp(word, start, end))
            }
        }
    }

    private fun formatAsMarkdown(result: TranscriptionResult, audioFileName: String): String {
        val sb = StringBuilder()
        val currentDate = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault()).format(Date())

        // Create title from first few words
        val title = if (result.text.isNotEmpty()) {
            result.text.take(50).trim()
        } else {
            "Voice Note"
        }

        // Add frontmatter
        sb.append("---\n")
        sb.append("title: \"$title\"\n")
        sb.append("date: $currentDate\n")
        sb.append("tags: [voice, transcription, offline]\n")
        sb.append("---\n\n")

        // Add content
        sb.append("# $title\n\n")

        // Add audio file reference
        sb.append("## Audio Recording\n\n")
        sb.append("![[${audioFileName}]]\n\n")

        // Add transcription
        sb.append("## Transcription (Offline)\n\n")
        sb.append(result.text)

        // Add timestamps section if we have word timestamps
        if (result.words.isNotEmpty()) {
            sb.append("\n\n## Timestamps\n\n")
            sb.append("| Word | Start Time | End Time |\n")
            sb.append("|------|------------|----------|\n")

            result.words.forEach { word ->
                sb.append("| ${word.word} | ${formatTime(word.start)} | ${formatTime(word.end)} |\n")
            }
        }

        return sb.toString()
    }

    private fun formatTime(seconds: Float): String {
        val minutes = (seconds / 60).toInt()
        val remainingSeconds = seconds % 60
        return String.format("%d:%05.2f", minutes, remainingSeconds)
    }

    private fun saveAudioToVault(
        audioPath: String,
        audioFileName: String,
        vaultUriString: String,
        attachmentFolderPath: String
    ): Uri? {
        try {
            val vaultUri = vaultUriString.toUri()
            val vaultDoc = DocumentFile.fromTreeUri(this, vaultUri) ?: return null

            // Get the attachment folder path from appDataStore if not provided
            val appDataStore = AppModule.provideAppDataStoreRepository(applicationContext)
            val savedAttachmentFolder = if (attachmentFolderPath.isEmpty()) {
                appDataStore.getStringValue(Constants.Preferences.OBSIDIAN_ATTACHMENT_FOLDER, "")
            } else {
                attachmentFolderPath
            }

            Log.d(TAG, "Original attachment folder path: $savedAttachmentFolder")

            // Navigate to attachment folder or create it if it doesn't exist
            var attachmentDir = vaultDoc

            if (savedAttachmentFolder.isNotEmpty()) {
                // Process the path - if it starts with a dot, it's relative to the vault root
                val processedPath = if (savedAttachmentFolder.startsWith(".")) {
                    // Remove the leading dot and slash if present
                    savedAttachmentFolder.removePrefix("./").removePrefix(".")
                } else {
                    savedAttachmentFolder
                }
                Log.d(TAG, "Processed attachment folder path: $processedPath")

                // Split the path and create each folder
                val folders = processedPath.split("/")
                for (folder in folders) {
                    if (folder.isEmpty()) continue

                    // Find or create each folder in the path
                    val nextFolder =
                        attachmentDir.findFile(folder) ?: attachmentDir.createDirectory(folder)
                    if (nextFolder == null) {
                        Log.e(TAG, "Failed to create folder: $folder")
                        return null
                    }
                    attachmentDir = nextFolder
                }
            } else {
                // Default to "attachments" folder if no specific path is set
                var attachmentsDir = vaultDoc.findFile("attachments")
                if (attachmentsDir == null) {
                    attachmentsDir = vaultDoc.createDirectory("attachments")
                    if (attachmentsDir == null) {
                        Log.e(TAG, "Failed to create attachments folder")
                        return null
                    }
                }
                attachmentDir = attachmentsDir
            }

            // Create the file in the attachment folder with appropriate MIME type
            val mimeType = when {
                audioFileName.endsWith(".3gp") -> "audio/3gpp"
                audioFileName.endsWith(".wav") -> "audio/wav"
                audioFileName.endsWith(".m4a") -> "audio/mp4a-latm"
                else -> "audio/*"
            }
            val newFile = attachmentDir.createFile(mimeType, audioFileName) ?: return null

            // Copy the content from the source to the destination
            val audioFile = File(audioPath)
            if (!audioFile.exists()) {
                Log.e(TAG, "Audio file does not exist: $audioPath")
                return null
            }

            audioFile.inputStream().use { inputStream ->
                contentResolver.openOutputStream(newFile.uri)?.use { outputStream ->
                    inputStream.copyTo(outputStream)
                }
            }

            return newFile.uri
        } catch (e: Exception) {
            Log.e(TAG, "Error saving audio to vault", e)
            return null
        }
    }

    private fun saveToObsidianVault(content: String, vaultUriString: String): Uri? {
        try {
            val vaultUri = vaultUriString.toUri()
            val vaultDoc = DocumentFile.fromTreeUri(this, vaultUri) ?: return null

            // Create Clippings folder if needed
            var clippingsFolder = vaultDoc.findFile("Clippings")
            if (clippingsFolder == null) {
                clippingsFolder = vaultDoc.createDirectory("Clippings")
                if (clippingsFolder == null) {
                    Log.e(TAG, "Failed to create Clippings folder")
                    return null
                }
            }

            // Create filename with timestamp
            val timestamp = SimpleDateFormat("yyyy-MM-dd-HHmmss", Locale.US).format(Date())
            val fileName = "Voice-Offline-$timestamp.md"

            // Create file
            val file = clippingsFolder.createFile("text/markdown", fileName)
            if (file == null) {
                Log.e(TAG, "Failed to create file")
                return null
            }

            // Write content
            contentResolver.openOutputStream(file.uri)?.use { outputStream ->
                outputStream.write(content.toByteArray())
            }

            return file.uri
        } catch (e: Exception) {
            Log.e(TAG, "Error saving to Obsidian vault", e)
            return null
        }
    }

    private fun addLinkToTodayNote(fileUri: Uri, vaultUriString: String) {
        // Extract a title from the file URI
        val fileName = fileUri.lastPathSegment?.substringAfterLast('/') ?: "Voice Note"
        val title = fileName.substringBeforeLast('.').replace("-", " ")

        // Start the TodayNoteService to add the link
        TodayNoteService.startLinkService(
            context = this,
            fileUri = fileUri.toString(),
            fileTitle = title,
            vaultUri = vaultUriString,
            fileType = "voice-offline"
        )
    }

    private fun showCompletionNotification(fileUri: Uri) {
        // Create a notification to show that the transcription is complete
        val notificationManager =
            getSystemService(NOTIFICATION_SERVICE) as NotificationManager

        val notificationBuilder = NotificationCompat.Builder(this, NOTIFICATION_CHANNEL_ID)
            .setContentTitle("Offline Voice Transcription Saved")
            .setContentText("Tap to view in Obsidian")
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setAutoCancel(true)

        // Create an intent to open the file in Obsidian
        val openIntent = Intent(Intent.ACTION_VIEW).apply {
            data = fileUri
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
        }

        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            openIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        notificationBuilder.setContentIntent(pendingIntent)

        // Show the notification
        notificationManager.notify(notificationId + 1000, notificationBuilder.build())
    }

    data class TranscriptionResult(
        val text: String,
        val words: List<WordTimestamp>
    )

    data class WordTimestamp(
        val word: String,
        val start: Float,
        val end: Float
    )
}
