package com.devindie.keepsidian

import android.content.Context
import android.content.Intent
import androidx.glance.GlanceId
import androidx.glance.action.ActionParameters
import androidx.glance.appwidget.action.ActionCallback

/**
 * Action callback for the camera button in widgets
 * This launches the CameraImageActivity to take a picture and save it to Obsidian vault
 * without OCR processing
 */
class CameraImageAction : ActionCallback {
    override suspend fun onAction(
        context: Context, glanceId: GlanceId, parameters: ActionParameters
    ) {
        // Launch camera image activity
        val intent = Intent(context, CameraImageActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        context.startActivity(intent)
    }
}
