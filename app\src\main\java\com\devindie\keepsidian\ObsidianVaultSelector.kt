package com.devindie.keepsidian

import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.documentfile.provider.DocumentFile
import com.devindie.keepsidian.analytics.AnalyticsHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive

/**
 * Data class to hold configuration information
 */
data class ObsidianConfig(
    val vaultUri: Uri? = null,
    val vaultName: String = "",
    val attachmentFolder: String? = null,
    val dailyNotesFolder: String? = null,
    val dailyNotesFormat: String? = null,
    val saveLocation: String? = null,
    val isConfigured: Boolean = false,
    val validationError: String? = null
)

@Composable
fun ObsidianVaultSelector(
    onVaultSelected: (
        vaultUri: Uri,
        attachmentFolderPath: String?,
        dailyNotesFolder: String?,
        dailyNotesFormat: String?,
        saveLocation: String?
    ) -> Unit
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val appDataStore = AppModule.provideAppDataStoreRepository(context)

    // State for loading indicator
    var isLoading by remember { mutableStateOf(false) }

    // State for configuration
    var config by remember { mutableStateOf(ObsidianConfig()) }

    // State variables for configuration

    // Check if we already have a configured vault
    LaunchedEffect(Unit) {
        val savedVaultUri =
            appDataStore.getStringValue(Constants.Preferences.OBSIDIAN_VAULT_URI, "")
        val savedAttachmentFolder =
            appDataStore.getStringValue(Constants.Preferences.OBSIDIAN_ATTACHMENT_FOLDER, "")
        val savedDailyNotesFolder =
            appDataStore.getStringValue(Constants.Preferences.OBSIDIAN_DAILY_NOTES_FOLDER, "")
        val savedDailyNotesFormat =
            appDataStore.getStringValue(Constants.Preferences.OBSIDIAN_DAILY_NOTES_FORMAT, "")
        val savedSaveLocation =
            appDataStore.getStringValue(Constants.Preferences.OBSIDIAN_SAVE_LOCATION, "Clippings")

        if (savedVaultUri.isNotEmpty()) {
            val uri = Uri.parse(savedVaultUri)
            val vaultName = getVaultName(context, uri)
            config = ObsidianConfig(
                vaultUri = uri,
                vaultName = vaultName,
                attachmentFolder = savedAttachmentFolder.ifEmpty { null },
                dailyNotesFolder = savedDailyNotesFolder.ifEmpty { null },
                dailyNotesFormat = savedDailyNotesFormat.ifEmpty { null },
                saveLocation = savedSaveLocation,
                isConfigured = true
            )
        }
    }

    // Folder picker launcher
    val vaultPicker = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.OpenDocumentTree()
    ) { uri: Uri? ->
        uri?.let {
            // Get persistent permissions
            context.applicationContext.contentResolver.takePersistableUriPermission(
                uri,
                Intent.FLAG_GRANT_READ_URI_PERMISSION or
                        Intent.FLAG_GRANT_WRITE_URI_PERMISSION
            )

            // Set loading state
            isLoading = true

            coroutineScope.launch {
                // Check if the selected folder has an .obsidian folder
                val obsidianFolder = findObsidianFolder(context, uri)

                if (obsidianFolder == null) {
                    // No .obsidian folder found - show error
                    config = config.copy(
                        validationError = context.getString(R.string.invalid_vault_message),
                        isConfigured = false // Ensure vault is not considered configured
                    )
                    isLoading = false
                    // Track vault configuration failure
                    AnalyticsHelper.trackVaultConfiguration(false)
                    return@launch
                }

                // Clear any previous validation errors
                config = config.copy(validationError = null)

                // Read Obsidian config files
                val (attachmentFolder, dailyNotesFolder, dailyNotesFormat) = readObsidianConfig(
                    context,
                    uri
                )

                // Get vault name
                val vaultName = getVaultName(context, uri)

                // Set default save location to "Clippings"
                val saveLocation = "Clippings"

                // Save vault URI
                appDataStore.putString(Constants.Preferences.OBSIDIAN_VAULT_URI, uri.toString())

                // Save attachment folder path if found
                if (attachmentFolder != null) {
                    appDataStore.putString(
                        Constants.Preferences.OBSIDIAN_ATTACHMENT_FOLDER,
                        attachmentFolder
                    )
                }

                // Save daily notes folder
                // Empty string means use vault root folder
                appDataStore.putString(
                    Constants.Preferences.OBSIDIAN_DAILY_NOTES_FOLDER,
                    dailyNotesFolder ?: ""
                )

                // Save daily notes format
                // Default to yyyy-MM-dd if not specified
                appDataStore.putString(
                    Constants.Preferences.OBSIDIAN_DAILY_NOTES_FORMAT,
                    dailyNotesFormat ?: "yyyy-MM-dd"
                )

                // Save the default save location
                appDataStore.putString(Constants.Preferences.OBSIDIAN_SAVE_LOCATION, saveLocation)

                // Update config state
                config = ObsidianConfig(
                    vaultUri = uri,
                    vaultName = vaultName,
                    attachmentFolder = attachmentFolder,
                    dailyNotesFolder = dailyNotesFolder,
                    dailyNotesFormat = dailyNotesFormat,
                    saveLocation = saveLocation,
                    isConfigured = true
                )

                // Stop loading
                isLoading = false

                // Track vault configuration success
                AnalyticsHelper.trackVaultConfiguration(true)

                // Return results to caller
                onVaultSelected(
                    uri,
                    attachmentFolder,
                    dailyNotesFolder,
                    dailyNotesFormat,
                    saveLocation
                )
            }
        }
    }

    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Vault selector button with conditional styling
        Box(
            modifier = Modifier.fillMaxWidth(),
            contentAlignment = Alignment.Center
        ) {
            // Only use animations if vault is not configured
            if (!config.isConfigured) {
                // Create pulsing animation
                val infiniteTransition = rememberInfiniteTransition(label = "buttonPulse")

                // Create border animation
                val borderAlpha by infiniteTransition.animateFloat(
                    initialValue = 0.3f,
                    targetValue = 0.8f,
                    animationSpec = infiniteRepeatable(
                        animation = tween(1500),
                        repeatMode = RepeatMode.Reverse
                    ),
                    label = "borderAlpha"
                )

                // Create gradient colors for the button
                val gradientColors = listOf(
                    Color(0xFF7C3AED), // Purple
                    Color(0xFF4F46E5)  // Indigo
                )

                // Animated button for unconfigured state
                Row(
                    modifier = Modifier
                        .shadow(
                            elevation = 4.dp,
                            shape = RoundedCornerShape(24.dp)
                        )
                        .clip(RoundedCornerShape(24.dp))
                        .background(
                            brush = Brush.linearGradient(gradientColors),
                            shape = RoundedCornerShape(24.dp)
                        )
                        .border(
                            width = 2.dp,
                            color = Color.White.copy(alpha = borderAlpha),
                            shape = RoundedCornerShape(24.dp)
                        )
                        .defaultMinSize(
                            minWidth = 220.dp,
                            minHeight = 56.dp
                        )
                        .clickable {
                            vaultPicker.launch(null) // Launch with no initial URI
                        }
                        .padding(horizontal = 16.dp, vertical = 16.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Center
                ) {
                    // Obsidian icon with original colors
                    Icon(
                        painter = painterResource(id = R.drawable.obsidian_icon),
                        contentDescription = stringResource(R.string.obsidian_icon_description),
                        tint = Color.Unspecified, // Use Color.Unspecified to show original drawable colors
                        modifier = Modifier
                            .size(36.dp)
                            .padding(end = 8.dp)
                    )

                    // Button text
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = stringResource(R.string.select_vault),
                            color = Color.White,
                            fontWeight = FontWeight.Bold,
                            fontSize = 16.sp
                        )

                        Text(
                            text = stringResource(R.string.must_contain_obsidian_folder),
                            color = Color.White.copy(alpha = 0.8f),
                            fontSize = 12.sp
                        )
                    }
                }
            } else {
                // Simple button for configured state
                Row(
                    modifier = Modifier
                        .clip(RoundedCornerShape(16.dp))
                        .background(
                            color = MaterialTheme.colorScheme.surfaceVariant,
                            shape = RoundedCornerShape(16.dp)
                        )
                        .border(
                            width = 1.dp,
                            color = MaterialTheme.colorScheme.outline,
                            shape = RoundedCornerShape(16.dp)
                        )
                        .defaultMinSize(
                            minWidth = 200.dp,
                            minHeight = 48.dp
                        )
                        .clickable {
                            vaultPicker.launch(null) // Launch with no initial URI
                        }
                        .padding(horizontal = 16.dp, vertical = 8.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Center
                ) {
                    // Obsidian icon with original colors
                    Icon(
                        painter = painterResource(id = R.drawable.obsidian_icon),
                        contentDescription = stringResource(R.string.obsidian_icon_description),
                        tint = Color.Unspecified, // Use Color.Unspecified to show original drawable colors
                        modifier = Modifier
                            .size(28.dp)
                            .padding(end = 8.dp)
                    )

                    // Button text
                    Text(
                        text = stringResource(R.string.change_obsidian_vault),
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        fontWeight = FontWeight.Medium,
                        fontSize = 14.sp
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Error message
        config.validationError?.let { error ->
            Card(
                modifier = Modifier
                    .fillMaxWidth(0.9f)
                    .padding(vertical = 8.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.errorContainer
                ),
                shape = RoundedCornerShape(8.dp),
                elevation = CardDefaults.cardElevation(
                    defaultElevation = 4.dp
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    // Warning icon
                    Icon(
                        painter = painterResource(id = android.R.drawable.stat_notify_error),
                        contentDescription = "Error",
                        tint = MaterialTheme.colorScheme.error,
                        modifier = Modifier
                            .size(36.dp)
                            .padding(bottom = 8.dp)
                    )

                    Text(
                        text = stringResource(R.string.invalid_vault_title),
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.error,
                        textAlign = TextAlign.Center
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = error,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onErrorContainer,
                        textAlign = TextAlign.Center
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Try Again button
                    Row(
                        modifier = Modifier
                            .clip(RoundedCornerShape(16.dp))
                            .background(
                                color = MaterialTheme.colorScheme.error.copy(alpha = 0.1f),
                                shape = RoundedCornerShape(16.dp)
                            )
                            .border(
                                width = 1.dp,
                                color = MaterialTheme.colorScheme.error,
                                shape = RoundedCornerShape(16.dp)
                            )
                            .clickable {
                                vaultPicker.launch(null) // Launch folder picker again
                            }
                            .padding(horizontal = 16.dp, vertical = 8.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center
                    ) {
                        Text(
                            text = stringResource(R.string.select_vault),
                            color = MaterialTheme.colorScheme.error,
                            fontWeight = FontWeight.Medium,
                            fontSize = 14.sp
                        )
                    }
                }
            }
        }

        // Loading indicator
        if (isLoading) {
            Text(
                text = stringResource(R.string.reading_configuration),
                style = MaterialTheme.typography.bodyMedium,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(vertical = 8.dp)
            )
            LinearProgressIndicator(
                modifier = Modifier
                    .fillMaxWidth(0.8f)
                    .padding(vertical = 8.dp)
            )
        }

        // Configuration display
        if (config.isConfigured && !isLoading) {
            Card(
                modifier = Modifier
                    .fillMaxWidth(0.9f)
                    .padding(vertical = 8.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.7f)
                ),
                shape = RoundedCornerShape(8.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    // Title row with refresh button
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // Success icon
                            Icon(
                                imageVector = Icons.Filled.CheckCircle,
                                contentDescription = "Success",
                                tint = MaterialTheme.colorScheme.primary,
                                modifier = Modifier
                                    .size(24.dp)
                                    .padding(end = 8.dp)
                            )

                            Text(
                                text = stringResource(R.string.vault_configuration),
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.Bold,
                                color = MaterialTheme.colorScheme.onPrimaryContainer
                            )
                        }

                        // Refresh button
                        Box(
                            modifier = Modifier
                                .clip(CircleShape)
                                .background(MaterialTheme.colorScheme.primary.copy(alpha = 0.1f))
                                .clickable {
                                    // Only refresh if we have a valid URI
                                    config.vaultUri?.let { uri ->
                                        isLoading = true
                                        coroutineScope.launch {
                                            // Verify that the .obsidian folder still exists
                                            val obsidianFolder = findObsidianFolder(context, uri)

                                            if (obsidianFolder == null) {
                                                // No .obsidian folder found - show error
                                                config = config.copy(
                                                    validationError = context.getString(R.string.vault_no_longer_valid),
                                                    isConfigured = false // Ensure vault is not considered configured
                                                )
                                                isLoading = false
                                                // Track vault configuration failure during refresh
                                                AnalyticsHelper.trackVaultConfiguration(false)
                                                return@launch
                                            }

                                            // Clear any previous validation errors
                                            config = config.copy(validationError = null)

                                            val (attachmentFolder, dailyNotesFolder, dailyNotesFormat) = readObsidianConfig(
                                                context,
                                                uri
                                            )

                                            // Get the current save location or use default
                                            val saveLocation = appDataStore.getStringValue(
                                                Constants.Preferences.OBSIDIAN_SAVE_LOCATION,
                                                "Clippings"
                                            )

                                            // Save attachment folder path if found
                                            if (attachmentFolder != null) {
                                                appDataStore.putString(
                                                    Constants.Preferences.OBSIDIAN_ATTACHMENT_FOLDER,
                                                    attachmentFolder
                                                )
                                            }

                                            // Save daily notes folder
                                            // Empty string means use vault root folder
                                            appDataStore.putString(
                                                Constants.Preferences.OBSIDIAN_DAILY_NOTES_FOLDER,
                                                dailyNotesFolder ?: ""
                                            )

                                            // Save daily notes format
                                            // Default to yyyy-MM-dd if not specified
                                            appDataStore.putString(
                                                Constants.Preferences.OBSIDIAN_DAILY_NOTES_FORMAT,
                                                dailyNotesFormat ?: "yyyy-MM-dd"
                                            )

                                            // Update config state
                                            config = config.copy(
                                                attachmentFolder = attachmentFolder,
                                                dailyNotesFolder = dailyNotesFolder,
                                                dailyNotesFormat = dailyNotesFormat,
                                                saveLocation = saveLocation
                                            )

                                            isLoading = false

                                            // Track successful vault configuration refresh
                                            AnalyticsHelper.trackVaultConfiguration(true)
                                        }
                                    }
                                }
                                .padding(8.dp)
                        ) {
                            Text(
                                text = stringResource(R.string.refresh_configuration),
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.primary
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    // Vault name
                    Row(
                        modifier = Modifier.padding(vertical = 4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Vault: ",
                            style = MaterialTheme.typography.bodyMedium,
                            fontWeight = FontWeight.Bold
                        )
                        Text(
                            text = config.vaultName,
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }

                    // Attachment folder
                    Row(
                        modifier = Modifier.padding(vertical = 4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Attachments: ",
                            style = MaterialTheme.typography.bodyMedium,
                            fontWeight = FontWeight.Bold
                        )
                        Text(
                            text = config.attachmentFolder ?: "Default",
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }

                    // Daily notes folder
                    Row(
                        modifier = Modifier.padding(vertical = 4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Daily Notes: ",
                            style = MaterialTheme.typography.bodyMedium,
                            fontWeight = FontWeight.Bold
                        )
                        Text(
                            text = if (config.dailyNotesFolder.isNullOrEmpty()) "Root" else config.dailyNotesFolder.orEmpty(),
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }

                    // Daily notes format
                    Row(
                        modifier = Modifier.padding(vertical = 4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Daily Notes Format: ",
                            style = MaterialTheme.typography.bodyMedium,
                            fontWeight = FontWeight.Bold
                        )
                        Text(
                            text = if (config.dailyNotesFormat.isNullOrEmpty()) "yyyy-MM-dd" else config.dailyNotesFormat.orEmpty(),
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }

                    // Save location
                    Row(
                        modifier = Modifier.padding(vertical = 4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Save Location: ",
                            style = MaterialTheme.typography.bodyMedium,
                            fontWeight = FontWeight.Bold
                        )
                        Text(
                            text = config.saveLocation ?: "Clippings",
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    // Info text with green background (more friendly)
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 8.dp)
                            .background(
                                MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f),
                                RoundedCornerShape(4.dp)
                            )
                            .padding(8.dp),
                    ) {
                        // Success message
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.padding(bottom = 4.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Filled.CheckCircle,
                                contentDescription = "Success",
                                tint = MaterialTheme.colorScheme.primary,
                                modifier = Modifier
                                    .size(16.dp)
                                    .padding(end = 4.dp)
                            )

                            Text(
                                text = "Valid Obsidian vault successfully configured!",
                                style = MaterialTheme.typography.bodySmall,
                                fontWeight = FontWeight.Bold,
                                color = MaterialTheme.colorScheme.primary
                            )
                        }

                        Spacer(modifier = Modifier.height(4.dp))

                        Text(
                            text = stringResource(R.string.refresh_settings_info),
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.primary
                        )

                        Spacer(modifier = Modifier.height(4.dp))

                        Text(
                            text = stringResource(R.string.valid_vault_note),
                            style = MaterialTheme.typography.bodySmall,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                }
            }
        }

        // End of component
    }
}

/**
 * Reads Obsidian configuration files to extract attachment folder, daily notes folder, and daily notes format
 */
private suspend fun readObsidianConfig(
    context: Context,
    vaultUri: Uri
): Triple<String?, String?, String?> = withContext(Dispatchers.IO) {
    val obsidianDir = findObsidianFolder(context, vaultUri)

    // If obsidianDir is null, we can't proceed with reading config files
    if (obsidianDir == null) {
        return@withContext Triple(null, "", "yyyy-MM-dd") // Default values
    }

    // Default values if config files don't exist
    var attachmentFolder: String? = null
    var dailyNotesFolder: String? = null
    var dailyNotesFormat: String? = null

    // Read app.json for attachment folder path
    obsidianDir.findFile("app.json")?.let { appJsonFile ->
        try {
            context.contentResolver.openInputStream(appJsonFile.uri)?.use { inputStream ->
                val appJsonContent = inputStream.bufferedReader().use { it.readText() }
                val appJson = Json.parseToJsonElement(appJsonContent).jsonObject

                // Extract attachmentFolderPath if it exists
                if (appJson.containsKey("attachmentFolderPath")) {
                    attachmentFolder = appJson["attachmentFolderPath"]?.jsonPrimitive?.content
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    // Read daily-notes.json for daily notes folder and format
    val dailyNotesFile = obsidianDir.findFile("daily-notes.json")

    if (dailyNotesFile != null) {
        try {
            context.contentResolver.openInputStream(dailyNotesFile.uri)?.use { inputStream ->
                val dailyNotesContent = inputStream.bufferedReader().use { it.readText() }
                val dailyNotesJson = Json.parseToJsonElement(dailyNotesContent).jsonObject

                // Extract folder if it exists and is not empty
                dailyNotesFolder = if (!dailyNotesJson.containsKey("folder")) {
                    "" // Empty string means use vault root folder
                } else {
                    val folderValue = dailyNotesJson["folder"]?.jsonPrimitive?.content
                    if (folderValue.isNullOrEmpty()) {
                        "" // Empty string means use vault root folder
                    } else {
                        folderValue
                    }
                }

                // Extract format if it exists
                dailyNotesFormat = if (!dailyNotesJson.containsKey("format")) {
                    "yyyy-MM-dd" // Default format
                } else {
                    val formatValue = dailyNotesJson["format"]?.jsonPrimitive?.content
                    if (formatValue.isNullOrEmpty()) {
                        "yyyy-MM-dd" // Default format
                    } else {
                        formatValue
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
            // Set default values in case of exception
            dailyNotesFolder = "" // Use vault root folder
            dailyNotesFormat = "yyyy-MM-dd" // Default format
        }
    } else {
        // If daily-notes.json doesn't exist, use default values
        dailyNotesFolder = "" // Use vault root folder
        dailyNotesFormat = "yyyy-MM-dd" // Default format
    }

    Triple(attachmentFolder, dailyNotesFolder, dailyNotesFormat)
}

/**
 * Finds the .obsidian folder in the vault
 */
private fun findObsidianFolder(context: Context, vaultUri: Uri): DocumentFile? {
    val vaultDir = DocumentFile.fromTreeUri(context, vaultUri) ?: return null
    return vaultDir.findFile(".obsidian")
}

/**
 * Extracts the vault name from the URI
 */
private fun getVaultName(context: Context, vaultUri: Uri): String {
    val vaultDir = DocumentFile.fromTreeUri(context, vaultUri)
    return vaultDir?.name ?: vaultUri.lastPathSegment ?: "Obsidian Vault"
}