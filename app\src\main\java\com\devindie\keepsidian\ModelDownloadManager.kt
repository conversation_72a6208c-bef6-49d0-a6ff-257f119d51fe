package com.devindie.keepsidian

import android.content.Context
import android.net.Uri
import android.os.Environment
import android.os.Handler
import android.util.Log
import android.widget.Toast
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.devindie.keepsidian.services.VoskTranscriptionService
import okhttp3.Call
import okhttp3.Callback
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.util.concurrent.TimeUnit
import java.util.zip.ZipInputStream

/**
 * Manages downloading and extracting the Vosk model for offline speech recognition
 */
class ModelDownloadManager(private val context: Context) : DefaultLifecycleObserver {
    companion object {
        private const val TAG = "ModelDownloadManager"

        // Default model URL for English
        private const val DEFAULT_MODEL_URL = VoskTranscriptionService.DEFAULT_MODEL_URL

        // Model file name
        private const val MODEL_ZIP_FILENAME = VoskTranscriptionService.MODEL_ZIP_FILENAME
        private const val MODEL_FOLDER_NAME = VoskTranscriptionService.MODEL_FOLDER_NAME

        // Check if model exists and has the required files
        fun isModelDownloaded(context: Context): Boolean {
            val modelDir = File(context.filesDir, MODEL_FOLDER_NAME)

            // First check if the directory exists and is not empty
            if (!modelDir.exists() || modelDir.list()?.isEmpty() == true) {
                Log.d(TAG, "Model check: directory doesn't exist or is empty at ${modelDir.absolutePath}")
                return false
            }

            // Check for specific model files or directories that should be present
            val modelFiles = modelDir.listFiles()
            if (modelFiles == null) {
                Log.d(TAG, "Model check: cannot list files in directory at ${modelDir.absolutePath}")
                return false
            }

            // Log the contents for debugging
            Log.d(TAG, "Model check: directory contains: ${modelFiles.joinToString { it.name }}")

            // Check for essential model directories
            val hasRequiredDirs = modelFiles.any { it.isDirectory && it.name == "am" } &&
                                 (modelFiles.any { it.isDirectory && it.name == "conf" } ||
                                  modelFiles.any { it.name == "conf.c" } ||
                                  modelFiles.any { it.name == "mfcc.conf" })

            Log.d(TAG, "Model check: hasRequiredDirs=${hasRequiredDirs}, path=${modelDir.absolutePath}")
            return hasRequiredDirs
        }
    }

    private var downloadCall: Call? = null
    private var downloadCallback: ((Boolean) -> Unit)? = null
    private var progressCallback: ((Int) -> Unit)? = null

    // OkHttp client with timeout settings
    private val okHttpClient = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .build()

    // Lifecycle methods
    override fun onDestroy(owner: LifecycleOwner) {
        // Cancel any ongoing download
        cancelDownload()
        super.onDestroy(owner)
    }

    private fun cancelDownload() {
        Log.d(TAG, "cancelDownload: Cancelling any ongoing download")
        downloadCall?.cancel()
        downloadCall = null
    }

    /**
     * Downloads the Vosk model if it doesn't already exist
     * @param callback Called when download completes (success or failure)
     * @param progressUpdate Called with download progress percentage (0-100)
     */
    fun downloadModelIfNeeded(
        callback: (Boolean) -> Unit,
        progressUpdate: ((Int) -> Unit)? = null
    ) {
        Log.d(TAG, "downloadModelIfNeeded: Starting model check")

        // Check if model already exists
        if (isModelDownloaded(context)) {
            Log.d(TAG, "downloadModelIfNeeded: Model already exists, no need to download")
            callback(true)
            return
        }

        // Create model directory if it doesn't exist
        val modelDir = File(context.filesDir, MODEL_FOLDER_NAME)
        if (!modelDir.exists()) {
            Log.d(TAG, "downloadModelIfNeeded: Creating model directory at ${modelDir.absolutePath}")
            val created = modelDir.mkdirs()
            Log.d(TAG, "downloadModelIfNeeded: Directory creation result: $created")
        } else {
            Log.d(TAG, "downloadModelIfNeeded: Model directory already exists at ${modelDir.absolutePath}")
        }

        // Start download
        Log.d(TAG, "downloadModelIfNeeded: Setting up callbacks and starting download")
        downloadCallback = callback
        progressCallback = progressUpdate
        startDownload()
    }

    private fun startDownload() {
        try {
            Log.d(TAG, "startDownload: Beginning download process")

            // Create the destination file
            val downloadFile = File(context.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS), MODEL_ZIP_FILENAME)
            Log.d(TAG, "startDownload: Download destination: ${downloadFile.absolutePath}")

            // Create parent directory if it doesn't exist
            if (!downloadFile.parentFile?.exists()!!) {
                Log.d(TAG, "startDownload: Creating parent directory: ${downloadFile.parentFile?.absolutePath}")
                val created = downloadFile.parentFile?.mkdirs()
                Log.d(TAG, "startDownload: Parent directory creation result: $created")
            }

            // Create the request
            val request = Request.Builder()
                .url(DEFAULT_MODEL_URL)
                .build()
            Log.d(TAG, "startDownload: Created request for URL: ${DEFAULT_MODEL_URL}")

            // Show toast notification
            Toast.makeText(
                context,
                context.getString(R.string.downloading_speech_model),
                Toast.LENGTH_SHORT
            ).show()
            Log.d(TAG, "startDownload: Showed toast notification")

            // Start the download
            Log.d(TAG, "startDownload: Enqueueing download request")
            downloadCall = okHttpClient.newCall(request)
            downloadCall?.enqueue(object : Callback {
                override fun onFailure(call: Call, e: IOException) {
                    Log.e(TAG, "startDownload.onFailure: Download failed", e)
                    // Report failure on the main thread
                    Handler(context.mainLooper).post {
                        Log.d(TAG, "startDownload.onFailure: Showing failure toast on main thread")
                        Toast.makeText(
                            context,
                            context.getString(R.string.speech_model_download_failed),
                            Toast.LENGTH_SHORT
                        ).show()
                        downloadCallback?.invoke(false)
                        downloadCallback = null
                    }
                }

                override fun onResponse(call: Call, response: Response) {
                    Log.d(TAG, "startDownload.onResponse: Received response with code: ${response.code}")

                    if (!response.isSuccessful) {
                        Log.e(TAG, "startDownload.onResponse: Download failed with code: ${response.code}")
                        // Report failure on the main thread
                        Handler(context.mainLooper).post {
                            Log.d(TAG, "startDownload.onResponse: Showing failure toast on main thread")
                            Toast.makeText(
                                context,
                                context.getString(R.string.speech_model_download_failed),
                                Toast.LENGTH_SHORT
                            ).show()
                            downloadCallback?.invoke(false)
                            downloadCallback = null
                        }
                        return
                    }

                    // Download successful, save the file
                    Log.d(TAG, "startDownload.onResponse: Download successful, saving response to file")
                    saveResponseToFile(response, downloadFile)
                }
            })

            Log.d(TAG, "startDownload: Download request enqueued successfully")
        } catch (e: Exception) {
            Log.e(TAG, "startDownload: Error starting download", e)
            downloadCallback?.invoke(false)
            downloadCallback = null
        }
    }

    /**
     * Saves the response body to a file with progress tracking
     */
    private fun saveResponseToFile(response: Response, file: File) {
        try {
            Log.d(TAG, "saveResponseToFile: Starting to save response to file: ${file.absolutePath}")

            val body = response.body
            if (body == null) {
                Log.e(TAG, "saveResponseToFile: Response body is null")
                android.os.Handler(context.mainLooper).post {
                    Log.d(TAG, "saveResponseToFile: Invoking failure callback due to null body")
                    downloadCallback?.invoke(false)
                    downloadCallback = null
                }
                return
            }

            val contentLength = body.contentLength()
            Log.d(TAG, "saveResponseToFile: Content length: $contentLength bytes")

            var bytesRead = 0L
            var lastProgressUpdate = 0

            // Create output stream
            Log.d(TAG, "saveResponseToFile: Creating output stream")
            val outputStream = FileOutputStream(file)

            // Get input stream from response body
            Log.d(TAG, "saveResponseToFile: Getting input stream from response body")
            val inputStream = body.byteStream()

            // Buffer for reading data
            val buffer = ByteArray(8192)
            var bytes: Int

            // Read data and write to file
            Log.d(TAG, "saveResponseToFile: Starting to read data and write to file")
            while (inputStream.read(buffer).also { bytes = it } != -1) {
                outputStream.write(buffer, 0, bytes)
                bytesRead += bytes

                // Calculate and report progress
                if (contentLength > 0) {
                    val progress = (bytesRead * 100 / contentLength).toInt()
                    if (progress > lastProgressUpdate) {
                        lastProgressUpdate = progress
                        Log.d(TAG, "saveResponseToFile: Download progress: $progress% ($bytesRead/$contentLength bytes)")
                        android.os.Handler(context.mainLooper).post {
                            progressCallback?.invoke(progress)
                        }
                    }
                }
            }

            // Close streams
            Log.d(TAG, "saveResponseToFile: Closing streams")
            outputStream.flush()
            outputStream.close()
            inputStream.close()

            Log.d(TAG, "saveResponseToFile: Download completed successfully, total bytes: $bytesRead")

            // Extract the zip file on the main thread
            Log.d(TAG, "saveResponseToFile: Scheduling extraction on main thread")
            android.os.Handler(context.mainLooper).post {
                Log.d(TAG, "saveResponseToFile: Starting extraction on main thread")
                extractZipFile(Uri.fromFile(file))
            }
        } catch (e: Exception) {
            Log.e(TAG, "saveResponseToFile: Error saving file", e)
            android.os.Handler(context.mainLooper).post {
                Log.d(TAG, "saveResponseToFile: Showing failure toast on main thread")
                Toast.makeText(
                    context,
                    context.getString(R.string.speech_model_download_failed),
                    Toast.LENGTH_SHORT
                ).show()
                downloadCallback?.invoke(false)
                downloadCallback = null
            }
        }
    }

    private fun extractZipFile(zipUri: Uri) {
        try {
            Log.d(TAG, "extractZipFile: Starting extraction from URI: $zipUri")

            // Clear the model directory first to avoid any conflicts
            val modelDir = File(context.filesDir, MODEL_FOLDER_NAME)
            if (modelDir.exists()) {
                Log.d(TAG, "extractZipFile: Clearing existing model directory at ${modelDir.absolutePath}")
                modelDir.deleteRecursively()
            }

            // Create a fresh model directory
            Log.d(TAG, "extractZipFile: Creating model directory at ${modelDir.absolutePath}")
            val created = modelDir.mkdirs()
            Log.d(TAG, "extractZipFile: Directory creation result: $created")

            // Open the zip file
            Log.d(TAG, "extractZipFile: Opening zip file input stream")
            context.contentResolver.openInputStream(zipUri)?.use { inputStream ->
                val zipInputStream = ZipInputStream(inputStream)
                var zipEntry = zipInputStream.nextEntry
                var fileCount = 0
                var totalBytes = 0L

                // Check if the zip contains a root directory
                var rootDirName: String? = null

                // First pass to determine if there's a root directory
                while (zipEntry != null) {
                    val entryName = zipEntry.name
                    Log.d(TAG, "extractZipFile: First pass - Entry: $entryName")

                    if (rootDirName == null && entryName.contains("/") && !entryName.startsWith("__MACOSX")) {
                        rootDirName = entryName.split("/")[0]
                        Log.d(TAG, "extractZipFile: Detected root directory: $rootDirName")
                    }

                    zipInputStream.closeEntry()
                    zipEntry = zipInputStream.nextEntry
                }

                // Reset the zip input stream
                zipInputStream.close()
                context.contentResolver.openInputStream(zipUri)?.use { newInputStream ->
                    val newZipInputStream = ZipInputStream(newInputStream)
                    zipEntry = newZipInputStream.nextEntry

                    while (zipEntry != null) {
                        val entryName = zipEntry.name

                        // Skip macOS metadata files
                        if (entryName.startsWith("__MACOSX")) {
                            Log.d(TAG, "extractZipFile: Skipping macOS metadata: $entryName")
                            newZipInputStream.closeEntry()
                            zipEntry = newZipInputStream.nextEntry
                            continue
                        }

                        // Determine the target path - strip the root directory if needed
                        val targetPath = if (rootDirName != null && entryName.startsWith(rootDirName)) {
                            // Remove the root directory from the path
                            entryName.substring(rootDirName.length + 1)
                        } else {
                            entryName
                        }

                        // Skip empty paths
                        if (targetPath.isEmpty()) {
                            newZipInputStream.closeEntry()
                            zipEntry = newZipInputStream.nextEntry
                            continue
                        }

                        val newFile = File(modelDir, targetPath)
                        Log.d(TAG, "extractZipFile: Processing entry: $entryName -> $targetPath, isDirectory: ${zipEntry.isDirectory}")

                        // Create directories if needed
                        if (zipEntry.isDirectory) {
                            Log.d(TAG, "extractZipFile: Creating directory: ${newFile.absolutePath}")
                            val dirCreated = newFile.mkdirs()
                            Log.d(TAG, "extractZipFile: Directory creation result: $dirCreated")
                        } else {
                            // Create parent directories if they don't exist
                            if (newFile.parentFile != null && !newFile.parentFile!!.exists()) {
                                Log.d(TAG, "extractZipFile: Creating parent directories: ${newFile.parentFile?.absolutePath}")
                                val parentCreated = newFile.parentFile?.mkdirs()
                                Log.d(TAG, "extractZipFile: Parent directory creation result: $parentCreated")
                            }

                            // Extract file
                            Log.d(TAG, "extractZipFile: Extracting file to: ${newFile.absolutePath}")
                            var fileBytes = 0L
                            FileOutputStream(newFile).use { fileOutputStream ->
                                val buffer = ByteArray(4096)
                                var len: Int

                                while (newZipInputStream.read(buffer).also { len = it } > 0) {
                                    fileOutputStream.write(buffer, 0, len)
                                    fileBytes += len
                                    totalBytes += len
                                }
                            }
                            Log.d(TAG, "extractZipFile: Extracted ${fileBytes} bytes to ${newFile.absolutePath}")
                            fileCount++
                        }

                        newZipInputStream.closeEntry()
                        zipEntry = newZipInputStream.nextEntry
                    }

                    Log.d(TAG, "extractZipFile: Extraction complete. Extracted $fileCount files, total size: $totalBytes bytes")
                }
            }

            // Verify the model directory structure
            val modelContents = modelDir.listFiles()
            if (modelContents != null) {
                Log.d(TAG, "extractZipFile: Model directory contains: ${modelContents.joinToString { it.name }}")

                // Check for expected model files/directories
                val hasModelFiles = modelContents.any { it.isDirectory && (it.name == "am" || it.name == "conf" || it.name == "ivector") }
                if (!hasModelFiles) {
                    Log.e(TAG, "extractZipFile: Model directory doesn't contain expected model files")

                    // Check if there's a single subdirectory that might contain the model
                    val singleSubdir = modelContents.singleOrNull { it.isDirectory }
                    if (singleSubdir != null) {
                        Log.d(TAG, "extractZipFile: Found single subdirectory: ${singleSubdir.name}, checking contents")

                        val subdirContents = singleSubdir.listFiles()
                        if (subdirContents != null) {
                            Log.d(TAG, "extractZipFile: Subdirectory contains: ${subdirContents.joinToString { it.name }}")

                            // If this subdirectory contains the model files, move them up
                            val hasModelFilesInSubdir = subdirContents.any { it.isDirectory && (it.name == "am" || it.name == "conf" || it.name == "ivector") }
                            if (hasModelFilesInSubdir) {
                                Log.d(TAG, "extractZipFile: Found model files in subdirectory, moving them up")

                                // Move all files from subdirectory to model directory
                                subdirContents.forEach { file ->
                                    val target = File(modelDir, file.name)
                                    Log.d(TAG, "extractZipFile: Moving ${file.absolutePath} to ${target.absolutePath}")
                                    file.renameTo(target)
                                }

                                // Delete the now-empty subdirectory
                                singleSubdir.delete()
                            }
                        }
                    }
                }
            }

            // Delete the zip file
            val zipFile = File(context.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS), MODEL_ZIP_FILENAME)
            if (zipFile.exists()) {
                Log.d(TAG, "extractZipFile: Deleting zip file: ${zipFile.absolutePath}")
                val deleted = zipFile.delete()
                Log.d(TAG, "extractZipFile: Zip file deletion result: $deleted")
            } else {
                Log.d(TAG, "extractZipFile: Zip file not found at ${zipFile.absolutePath}")
            }

            // Final verification
            if (isModelDownloaded(context)) {
                Log.d(TAG, "extractZipFile: Model extracted successfully and verified")
                Toast.makeText(
                    context,
                    context.getString(R.string.offline_transcription_complete),
                    Toast.LENGTH_SHORT
                ).show()

                Log.d(TAG, "extractZipFile: Invoking success callback")
                downloadCallback?.invoke(true)
            } else {
                Log.e(TAG, "extractZipFile: Model extraction completed but verification failed")
                Toast.makeText(
                    context,
                    context.getString(R.string.speech_model_download_failed),
                    Toast.LENGTH_SHORT
                ).show()
                downloadCallback?.invoke(false)
            }
        } catch (e: Exception) {
            Log.e(TAG, "extractZipFile: Error extracting zip file", e)
            Toast.makeText(
                context,
                context.getString(R.string.speech_model_download_failed),
                Toast.LENGTH_SHORT
            ).show()
            downloadCallback?.invoke(false)
        } finally {
            Log.d(TAG, "extractZipFile: Clearing download callback")
            downloadCallback = null
        }
    }


}
