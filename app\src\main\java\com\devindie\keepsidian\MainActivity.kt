package com.devindie.keepsidian

import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.ActivityResultLauncher
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.devindie.keepsidian.analytics.AnalyticsHelper
import com.devindie.keepsidian.ui.theme.ObsidianExtTheme
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class MainActivity : ComponentActivity() {

    private lateinit var notificationPermissionLauncher: ActivityResultLauncher<String>
    private lateinit var storagePermissionLauncher: ActivityResultLauncher<Array<String>>
    private lateinit var audioPermissionLauncher: ActivityResultLauncher<String>

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Register notification permission launcher
        notificationPermissionLauncher =
            NotificationPermissionHelper.registerPermissionLauncher(this)

        // Register storage permission launcher
        storagePermissionLauncher =
            StoragePermissionHelper.registerPermissionLauncher(this)

        // Register audio permission launcher
        audioPermissionLauncher =
            AudioPermissionHelper.registerPermissionLauncher(this)

        // Request notification permission if needed
        NotificationPermissionHelper.requestNotificationPermissionIfNeeded(
            this, notificationPermissionLauncher
        )

        // Check if we need to show toasts for automatically enabled features
        val showOCREnabledToast = intent.getBooleanExtra(Constants.EXTRA_SHOW_OCR_ENABLED_TOAST, false)
        val showSpeechToTextEnabledToast = intent.getBooleanExtra(Constants.EXTRA_SHOW_SPEECH_TO_TEXT_ENABLED_TOAST, false)

        if (showOCREnabledToast) {
            Toast.makeText(
                this,
                getString(R.string.ocr_auto_enabled_toast),
                Toast.LENGTH_SHORT
            ).show()

            // Request storage permissions for OCR
            StoragePermissionHelper.requestStoragePermissionIfNeeded(
                this,
                storagePermissionLauncher
            )
        }

        if (showSpeechToTextEnabledToast) {
            Toast.makeText(
                this,
                getString(R.string.speech_to_text_auto_enabled_toast),
                Toast.LENGTH_SHORT
            ).show()

            // Request audio permissions for Speech-To-Text
            AudioPermissionHelper.requestAudioPermissionIfNeeded(
                this,
                audioPermissionLauncher
            )
        }

        enableEdgeToEdge()
        setContent {
            ObsidianExtTheme {
                MainScreen(
                    showOCREnabledToast = showOCREnabledToast,
                    showSpeechToTextEnabledToast = showSpeechToTextEnabledToast,
                    onEnableOCR = { enabled ->
                        if (enabled) {
                            // Request storage permissions when OCR is enabled
                            StoragePermissionHelper.requestStoragePermissionIfNeeded(
                                this@MainActivity,
                                storagePermissionLauncher
                            )
                        }
                    },
                    onEnableSpeechToText = { enabled ->
                        if (enabled) {
                            // Request audio permissions when Speech-To-Text is enabled
                            AudioPermissionHelper.requestAudioPermissionIfNeeded(
                                this@MainActivity,
                                audioPermissionLauncher
                            )
                        }
                    },
                    audioPermissionLauncher = audioPermissionLauncher
                )
            }
        }
    }
}

@Composable
fun MainScreen(
    showOCREnabledToast: Boolean = false,
    showSpeechToTextEnabledToast: Boolean = false,
    onEnableOCR: (Boolean) -> Unit,
    onEnableSpeechToText: (Boolean) -> Unit,
    audioPermissionLauncher: ActivityResultLauncher<String>? = null
) {
    val context = LocalContext.current
    val appDataStore = AppModule.provideAppDataStoreRepository(context)
    val coroutineScope = rememberCoroutineScope()

    // State to track if vault is configured
    var isVaultConfigured by remember { mutableStateOf(false) }

    // State for YouTube transcript toggle
    var autoYoutubeTranscript by remember {
        mutableStateOf(appDataStore.getBooleanValue(Constants.Preferences.AUTO_YOUTUBE_TRANSCRIPT, true))
    }

    // Check if vault is already configured on launch
    LaunchedEffect(Unit) {
        val savedVaultUri = appDataStore.getStringValue(Constants.Preferences.OBSIDIAN_VAULT_URI, "")
        isVaultConfigured = savedVaultUri.isNotEmpty()
    }

    Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
                .padding(16.dp)
                .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Mandatory configuration banner
            if (!isVaultConfigured) {
                Card(
                    modifier = Modifier
                        .fillMaxWidth(0.9f)
                        .padding(bottom = 16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.primaryContainer
                    ),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = stringResource(R.string.select_vault),
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold,
                            textAlign = TextAlign.Center
                        )

                        Spacer(modifier = Modifier.height(8.dp))

                        Text(
                            text = stringResource(R.string.vault_config_required),
                            style = MaterialTheme.typography.bodyMedium,
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }

            // Obsidian Vault Selector
            Box(
                modifier = Modifier.fillMaxWidth(),
                contentAlignment = Alignment.Center
            ) {
                ObsidianVaultSelector { vaultUri, attachmentFolder, dailyNotesFolder, dailyNotesFormat, saveLocation ->
                    // Use the configuration values
                    val actualAttachmentFolder = attachmentFolder ?: ""
                    val actualDailyNotesFolder = dailyNotesFolder ?: ""
                    val actualDailyNotesFormat = dailyNotesFormat ?: ""

                    // Log or display the results
                    Log.d("ObsidianVault", "Vault selected: $vaultUri")
                    Log.d("ObsidianVault", "Attachment folder: $actualAttachmentFolder")
                    Log.d("ObsidianVault", "Daily notes folder: $actualDailyNotesFolder")
                    Log.d("ObsidianVault", "Daily notes format: $actualDailyNotesFormat")

                    coroutineScope.launch(Dispatchers.IO) {
                        appDataStore.putString(
                            Constants.Preferences.OBSIDIAN_VAULT_URI, vaultUri.toString()
                        )
                        appDataStore.putString(
                            Constants.Preferences.OBSIDIAN_DAILY_NOTES_FOLDER, actualDailyNotesFolder
                        )
                        appDataStore.putString(
                            Constants.Preferences.OBSIDIAN_ATTACHMENT_FOLDER, actualAttachmentFolder
                        )
                        appDataStore.putString(
                            Constants.Preferences.OBSIDIAN_DAILY_NOTES_FORMAT, actualDailyNotesFormat
                        )
                        // Set default save location
                        appDataStore.putString(
                            Constants.Preferences.OBSIDIAN_SAVE_LOCATION, "Clippings"
                        )
                    }

                    // Update vault configuration state
                    isVaultConfigured = true
                }
            }

            Spacer(modifier = Modifier.height(24.dp))

            // Features section divider
            if (isVaultConfigured) {
                Text(
                    text = stringResource(R.string.features),
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
            } else {
                Text(
                    text = stringResource(R.string.features_vault_required),
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
            }

            HorizontalDivider(
                modifier = Modifier
                    .fillMaxWidth(0.9f)
                    .padding(bottom = 16.dp)
            )

            // YouTube transcript toggle
            Card(
                modifier = Modifier
                    .fillMaxWidth(0.9f)
                    .padding(vertical = 8.dp)
                    .alpha(if (isVaultConfigured) 1f else 0.6f),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                ),
                shape = RoundedCornerShape(8.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = stringResource(R.string.auto_youtube_transcript),
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = stringResource(R.string.auto_youtube_transcript_description),
                        style = MaterialTheme.typography.bodyMedium
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = if (autoYoutubeTranscript) stringResource(R.string.enabled) else stringResource(R.string.disabled),
                            style = MaterialTheme.typography.bodyMedium,
                            fontWeight = FontWeight.Medium
                        )

                        Switch(
                            checked = autoYoutubeTranscript,
                            onCheckedChange = { enabled ->
                                if (isVaultConfigured) {
                                    autoYoutubeTranscript = enabled
                                    coroutineScope.launch(Dispatchers.IO) {
                                        appDataStore.putBoolean(Constants.Preferences.AUTO_YOUTUBE_TRANSCRIPT, enabled)
                                    }
                                    // Track preference change
                                    AnalyticsHelper.trackPreferenceChange(Constants.Preferences.AUTO_YOUTUBE_TRANSCRIPT, enabled.toString())
                                }
                            },
                            enabled = false
                        )
                    }

                    if (!isVaultConfigured) {
                        Spacer(modifier = Modifier.height(8.dp))

                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .background(
                                    MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f),
                                    RoundedCornerShape(4.dp)
                                )
                                .padding(8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = stringResource(R.string.select_vault_to_enable),
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.primary
                            )
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // OCR Image Toggle
            OCRImageToggle(
                onEnableOCR = onEnableOCR,
                isEnabled = isVaultConfigured,
                autoEnable = showOCREnabledToast
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Speech-To-Text Toggle
            SpeechToTextToggle(
                onEnableSpeechToText = onEnableSpeechToText,
                isEnabled = isVaultConfigured,
                audioPermissionLauncher = audioPermissionLauncher,
                autoEnable = showSpeechToTextEnabledToast
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Offline Speech Recognition Toggle
            OfflineSpeechRecognitionToggle(
                onEnableOfflineRecognition = { enabled ->
                    // No additional permissions needed, just update the preference
                },
                isEnabled = isVaultConfigured
            )
        }
    }
}

@Composable
fun OCRImageToggle(
    onEnableOCR: (Boolean) -> Unit,
    isEnabled: Boolean = true,
    autoEnable: Boolean = false
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val appDataStore = AppModule.provideAppDataStoreRepository(context)

    // Get the current preference value or default to false (disabled)
    var enableOCRImages by remember {
        mutableStateOf(
            if (autoEnable) true
            else appDataStore.getBooleanValue(Constants.Preferences.ENABLE_OCR_IMAGES, false)
        )
    }

    // If autoEnable is true, update the preference value
    LaunchedEffect(autoEnable) {
        if (autoEnable && isEnabled) {
            coroutineScope.launch(Dispatchers.IO) {
                appDataStore.putBoolean(Constants.Preferences.ENABLE_OCR_IMAGES, true)
            }
            onEnableOCR(true)
            // Track preference change for auto-enable
            AnalyticsHelper.trackPreferenceChange(Constants.Preferences.ENABLE_OCR_IMAGES, "true")
        }
    }

    Card(
        modifier = Modifier
            .fillMaxWidth(0.9f)
            .padding(vertical = 8.dp)
            .alpha(if (isEnabled) 1f else 0.6f),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        ),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = stringResource(R.string.ocr_image_processing),
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = stringResource(R.string.ocr_description),
                style = MaterialTheme.typography.bodyMedium
            )

            Spacer(modifier = Modifier.height(16.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = if (enableOCRImages) stringResource(R.string.ocr_enabled) else stringResource(R.string.ocr_disabled),
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )

                Switch(
                    checked = enableOCRImages,
                    onCheckedChange = { enabled ->
                        if (isEnabled) {
                            enableOCRImages = enabled
                            coroutineScope.launch(Dispatchers.IO) {
                                appDataStore.putBoolean(Constants.Preferences.ENABLE_OCR_IMAGES, enabled)
                            }
                            onEnableOCR(enabled)
                            // Track preference change
                            AnalyticsHelper.trackPreferenceChange(Constants.Preferences.ENABLE_OCR_IMAGES, enabled.toString())
                        }
                    },
                    enabled = isEnabled
                )
            }

            if (!isEnabled) {
                Spacer(modifier = Modifier.height(8.dp))

                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f),
                            RoundedCornerShape(4.dp)
                        )
                        .padding(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = stringResource(R.string.select_vault_to_enable),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
            } else if (!enableOCRImages) {
                Spacer(modifier = Modifier.height(8.dp))

                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f),
                            RoundedCornerShape(4.dp)
                        )
                        .padding(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = stringResource(R.string.ocr_currently_disabled),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
            }
        }
    }
}