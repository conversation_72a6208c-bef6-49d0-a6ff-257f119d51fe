package com.devindie.keepsidian

import android.Manifest
import android.app.AlertDialog
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.activity.ComponentActivity
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat

/**
 * Helper class to handle notification permission requests for Android 13+
 */
object NotificationPermissionHelper {

    /**
     * Checks if the notification permission is granted
     */
    fun hasNotificationPermission(context: Context): Bo<PERSON>an {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.POST_NOTIFICATIONS
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            // Permission is automatically granted on versions below Android 13
            true
        }
    }

    /**
     * Registers the permission launcher for an activity
     */
    fun registerPermissionLauncher(activity: ComponentActivity): ActivityResultLauncher<String> {
        return activity.registerForActivityResult(
            ActivityResultContracts.RequestPermission()
        ) { isGranted ->
            if (isGranted) {
                // Permission granted, you can show notifications
            } else {
                // Permission denied, show a dialog explaining why notifications are important
                showNotificationPermissionRationale(activity)
            }
        }
    }

    /**
     * Requests notification permission if needed
     */
    fun requestNotificationPermissionIfNeeded(
        context: Context,
        permissionLauncher: ActivityResultLauncher<String>
    ) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (!hasNotificationPermission(context)) {
                permissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
            }
        }
    }

    /**
     * Shows a dialog explaining why notification permissions are important
     */
    private fun showNotificationPermissionRationale(activity: ComponentActivity) {
        AlertDialog.Builder(activity)
            .setTitle(activity.getString(R.string.notification_permission_title))
            .setMessage(activity.getString(R.string.notification_permission_message))
            .setPositiveButton(activity.getString(R.string.settings)) { _, _ ->
                // Open app settings so user can enable notifications
                val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                    data = Uri.fromParts("package", activity.packageName, null)
                }
                activity.startActivity(intent)
            }
            .setNegativeButton(activity.getString(R.string.cancel), null)
            .show()
    }
}
