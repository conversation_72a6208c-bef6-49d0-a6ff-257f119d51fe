# Fastlane Setup for VaultSnap

This document explains how to set up and use Fastlane for the VaultSnap Android project.

## Prerequisites

1. **Ruby**: Install Ruby (version 2.6 or higher)
2. **Bundler**: Install bundler gem
   ```bash
   gem install bundler
   ```

## Initial Setup

1. **Install dependencies**:
   ```bash
   bundle install
   ```

2. **Install Fastlane plugins**:
   ```bash
   bundle exec fastlane install_plugins
   ```

## Configuration

### 1. Environment Variables

Copy the `.env` file to `.env.local` and fill in your actual values:

```bash
cp fastlane/.env fastlane/.env.local
```

Edit `fastlane/.env.local` with your credentials:

```bash
# Firebase App Distribution
FIREBASE_APP_ID=your-firebase-app-id-here
FIREBASE_TOKEN=your-firebase-token-here

# Google Play Console
GOOGLE_PLAY_JSON_KEY_PATH=path/to/your/google-play-console-key.json

# Signing
KEYSTORE_PATH=path/to/your/keystore.jks
KEYSTORE_PASSWORD=your-keystore-password
KEY_ALIAS=your-key-alias
KEY_PASSWORD=your-key-password
```

### 2. Signing Configuration

Add your signing configuration to `local.properties`:

```properties
KEYSTORE_PATH=path/to/your/keystore.jks
KEYSTORE_PASSWORD=your-keystore-password
KEY_ALIAS=your-key-alias
KEY_PASSWORD=your-key-password
```

### 3. Google Play Console Setup

1. Create a service account in Google Cloud Console
2. Download the JSON key file
3. Grant necessary permissions in Google Play Console
4. Update the path in your environment variables

### 4. Firebase App Distribution Setup

1. Install Firebase CLI: `npm install -g firebase-tools`
2. Login: `firebase login:ci`
3. Get your Firebase token and app ID
4. Update the values in your environment variables

## Available Lanes

### Testing
- `bundle exec fastlane test` - Run all tests
- `bundle exec fastlane unit_tests` - Run unit tests only
- `bundle exec fastlane instrumented_tests` - Run instrumented tests

### Building
- `bundle exec fastlane build_debug` - Build debug APK
- `bundle exec fastlane build_release` - Build release APK
- `bundle exec fastlane build_aab` - Build release AAB (Android App Bundle)

### Deployment
- `bundle exec fastlane deploy_firebase` - Deploy to Firebase App Distribution
- `bundle exec fastlane deploy_internal` - Deploy to Google Play Internal Testing
- `bundle exec fastlane deploy_beta` - Deploy to Google Play Beta
- `bundle exec fastlane deploy_production` - Deploy to Google Play Production

### Version Management
- `bundle exec fastlane increment_version_code` - Increment version code
- `bundle exec fastlane increment_version_name` - Increment version name

### Complete Release Process
- `bundle exec fastlane release` - Complete release process (test, build, deploy, tag)

### Utility
- `bundle exec fastlane clean` - Clean build artifacts

## Typical Workflows

### Development Build
```bash
bundle exec fastlane build_debug
```

### Internal Testing
```bash
bundle exec fastlane deploy_firebase
```

### Beta Release
```bash
bundle exec fastlane deploy_beta
```

### Production Release
```bash
bundle exec fastlane release
```

## Troubleshooting

### Common Issues

1. **Ruby version issues**: Use rbenv or rvm to manage Ruby versions
2. **Bundle install fails**: Try `bundle install --path vendor/bundle`
3. **Gradle issues**: Ensure JAVA_HOME is set correctly
4. **Signing issues**: Verify keystore path and credentials

### Getting Help

- Fastlane documentation: https://docs.fastlane.tools/
- Android-specific guide: https://docs.fastlane.tools/getting-started/android/

## Security Notes

- Never commit `.env.local` or signing keys to version control
- Use environment variables for sensitive data
- Regularly rotate API keys and tokens
- Use separate keystores for debug and release builds
