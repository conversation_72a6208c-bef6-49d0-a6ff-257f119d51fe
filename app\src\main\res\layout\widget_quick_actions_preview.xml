<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal"
    android:background="@drawable/widget_background"
    android:gravity="center"
    android:padding="8dp">

    <ImageButton
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:src="@drawable/mic"
        android:background="@android:color/transparent"
        android:contentDescription="Speech to Text"
        android:layout_marginEnd="16dp" />

    <ImageButton
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:src="@drawable/ocr_camera"
        android:background="@android:color/transparent"
        android:contentDescription="OCR Image"
        android:layout_marginEnd="16dp" />

    <ImageButton
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:src="@drawable/camera"
        android:background="@android:color/transparent"
        android:contentDescription="Take Picture"
        android:layout_marginEnd="16dp" />

    <ImageButton
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:src="@drawable/add_task"
        android:background="@android:color/transparent"
        android:contentDescription="Add Calendar Event" />
</LinearLayout>
