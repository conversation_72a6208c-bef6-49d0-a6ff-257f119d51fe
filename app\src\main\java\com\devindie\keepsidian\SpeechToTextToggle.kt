package com.devindie.keepsidian

import android.app.Activity
import androidx.activity.ComponentActivity
import androidx.activity.result.ActivityResultLauncher
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.devindie.keepsidian.analytics.AnalyticsHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun SpeechToTextToggle(
    onEnableSpeechToText: (Boolean) -> Unit,
    isEnabled: Boolean = true,
    audioPermissionLauncher: ActivityResultLauncher<String>? = null,
    autoEnable: Boolean = false
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val appDataStore = AppModule.provideAppDataStoreRepository(context)

    // Get the current preference value or default to false (disabled)
    var enableSpeechToText by remember {
        mutableStateOf(
            if (autoEnable) true
            else appDataStore.getBooleanValue(Constants.Preferences.ENABLE_SPEECH_TO_TEXT, false)
        )
    }

    // If autoEnable is true, update the preference value
    LaunchedEffect(autoEnable) {
        if (autoEnable && isEnabled) {
            coroutineScope.launch(Dispatchers.IO) {
                appDataStore.putBoolean(Constants.Preferences.ENABLE_SPEECH_TO_TEXT, true)
            }
            onEnableSpeechToText(true)
            // Track preference change for auto-enable
            AnalyticsHelper.trackPreferenceChange(Constants.Preferences.ENABLE_SPEECH_TO_TEXT, "true")
        }
    }

    Card(
        modifier = Modifier
            .fillMaxWidth(0.9f)
            .padding(vertical = 8.dp)
            .alpha(if (isEnabled) 1f else 0.6f),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        ),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = stringResource(R.string.speech_to_text),
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = stringResource(R.string.speech_to_text_description, Constants.Preferences.MAX_RECORDING_TIME_SECONDS),
                style = MaterialTheme.typography.bodyMedium
            )

            Spacer(modifier = Modifier.height(16.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = if (enableSpeechToText) stringResource(R.string.enabled) else stringResource(R.string.disabled),
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )

                Switch(
                    checked = enableSpeechToText,
                    onCheckedChange = { enabled ->
                        if (isEnabled) {
                            enableSpeechToText = enabled
                            coroutineScope.launch(Dispatchers.IO) {
                                appDataStore.putBoolean(Constants.Preferences.ENABLE_SPEECH_TO_TEXT, enabled)
                            }
                            onEnableSpeechToText(enabled)
                            // Track preference change
                            AnalyticsHelper.trackPreferenceChange(Constants.Preferences.ENABLE_SPEECH_TO_TEXT, enabled.toString())

                            // Request audio permission if enabled and we have a launcher
                            if (enabled && audioPermissionLauncher != null && context is Activity) {
                                AudioPermissionHelper.requestAudioPermissionIfNeeded(
                                    context as ComponentActivity,
                                    audioPermissionLauncher
                                )
                            }
                        }
                    },
                    enabled = isEnabled
                )
            }

            if (!isEnabled) {
                Spacer(modifier = Modifier.height(8.dp))

                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f),
                            RoundedCornerShape(4.dp)
                        )
                        .padding(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = stringResource(R.string.select_vault_to_enable),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
            } else if (!enableSpeechToText) {
                Spacer(modifier = Modifier.height(8.dp))

                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f),
                            RoundedCornerShape(4.dp)
                        )
                        .padding(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Speech-To-Text is currently disabled. Enable this feature to record voice notes.",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
            }
        }
    }
}
