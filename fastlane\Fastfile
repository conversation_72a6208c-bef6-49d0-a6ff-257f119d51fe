# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:android)

platform :android do
  desc "Runs all the tests"
  lane :test do
    gradle(task: "test")
  end

  desc "Run unit tests"
  lane :unit_tests do
    gradle(task: "testDebugUnitTest")
  end

  desc "Run instrumented tests"
  lane :instrumented_tests do
    gradle(task: "connectedDebugAndroidTest")
  end

  desc "Build debug APK"
  lane :build_debug do
    gradle(task: "assembleDebug")
  end

  desc "Build release APK"
  lane :build_release do
    gradle(task: "assembleRelease")
  end

  desc "Build release AAB (Android App Bundle)"
  lane :build_aab do
    gradle(task: "bundleRelease")
  end

  desc "Deploy a new version to the Google Play Console (Internal Testing)"
  lane :deploy_internal do
    gradle(task: "bundleRelease")
    upload_to_play_store(
      track: 'internal',
      aab: lane_context[SharedValues::GRADLE_AAB_OUTPUT_PATH]
    )
  end

  desc "Deploy a new version to the Google Play Console (Beta)"
  lane :deploy_beta do
    gradle(task: "bundleRelease")
    upload_to_play_store(
      track: 'beta',
      aab: lane_context[SharedValues::GRADLE_AAB_OUTPUT_PATH]
    )
  end

  desc "Deploy a new version to the Google Play Console (Production)"
  lane :deploy_production do
    gradle(task: "bundleRelease")
    upload_to_play_store(
      track: 'production',
      aab: lane_context[SharedValues::GRADLE_AAB_OUTPUT_PATH]
    )
  end

  desc "Deploy to Firebase App Distribution"
  lane :deploy_firebase do
    gradle(task: "assembleRelease")
    firebase_app_distribution(
      app: ENV["FIREBASE_APP_ID"],
      groups: "internal-testers",
      release_notes: "New build from Fastlane"
    )
  end

  desc "Increment version code"
  lane :increment_version_code do
    increment_version_code_in_project_file(
      app_project_dir: "**/app"
    )
  end

  desc "Increment version name"
  lane :increment_version_name do
    increment_version_name_in_project_file(
      app_project_dir: "**/app"
    )
  end

  desc "Create a new release with version bump"
  lane :release do
    # Run tests first
    test

    # Increment version
    increment_version_code
    
    # Build release
    build_aab
    
    # Deploy to internal testing first
    deploy_internal
    
    # Commit version bump
    git_add(path: "app/build.gradle.kts")
    git_commit(path: "app/build.gradle.kts", message: "Version bump")
    
    # Create git tag
    add_git_tag(
      tag: "v#{get_version_name(app_project_dir: "**/app")}"
    )
    
    # Push to repository
    push_to_git_remote
  end

  desc "Clean build artifacts"
  lane :clean do
    gradle(task: "clean")
  end

  error do |lane, exception|
    # This block is called if there was an error running a lane
    UI.error("Error in lane #{lane}: #{exception}")
  end
end
