package com.devindie.keepsidian.services

import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.core.net.toUri
import androidx.documentfile.provider.DocumentFile
import com.devindie.keepsidian.AppModule
import com.devindie.keepsidian.Constants
import com.devindie.keepsidian.R
import com.devindie.keepsidian.analytics.AnalyticsHelper
import com.devindie.keepsidian.utils.WidgetUtils
import kotlinx.coroutines.launch
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class SpeechTranscriptionService : BaseProcessingService() {
    companion object {
        private const val TAG = "SpeechTranscriptionSvc"
        private const val NOTIFICATION_ID = 1004

        const val EXTRA_TEXT = "com.devindie.keepsidian.extra.TEXT"
        const val EXTRA_AUDIO_PATH = "com.devindie.keepsidian.extra.AUDIO_PATH"
        const val EXTRA_AUDIO_FILENAME = "com.devindie.keepsidian.extra.AUDIO_FILENAME"
        const val EXTRA_VAULT_URI = "com.devindie.keepsidian.extra.VAULT_URI"
        const val EXTRA_ATTACHMENT_FOLDER = "com.devindie.keepsidian.extra.ATTACHMENT_FOLDER"
        const val EXTRA_IS_ALREADY_FORMATTED = "com.devindie.keepsidian.extra.IS_ALREADY_FORMATTED"

        fun startService(
            context: Context,
            text: String,
            audioPath: String? = null,
            audioFileName: String? = null,
            vaultUri: String,
            attachmentFolder: String,
            isAlreadyFormatted: Boolean = false
        ) {
            val intent = Intent(context, SpeechTranscriptionService::class.java).apply {
                putExtra(EXTRA_TEXT, text)
                if (audioPath != null) putExtra(EXTRA_AUDIO_PATH, audioPath)
                if (audioFileName != null) putExtra(EXTRA_AUDIO_FILENAME, audioFileName)
                putExtra(EXTRA_VAULT_URI, vaultUri)
                putExtra(EXTRA_ATTACHMENT_FOLDER, attachmentFolder)
                putExtra(EXTRA_IS_ALREADY_FORMATTED, isAlreadyFormatted)
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }
    }

    override val serviceName: String = TAG
    override val notificationId: Int = NOTIFICATION_ID

    override fun onStartCommand(intent: Intent, flags: Int, startId: Int): Int {
        val startTime = System.currentTimeMillis()

        val text = intent.getStringExtra(EXTRA_TEXT) ?: ""
        val audioPath = intent.getStringExtra(EXTRA_AUDIO_PATH)
        val audioFileName = intent.getStringExtra(EXTRA_AUDIO_FILENAME)
        val vaultUriString = intent.getStringExtra(EXTRA_VAULT_URI) ?: ""
        val attachmentFolderPath = intent.getStringExtra(EXTRA_ATTACHMENT_FOLDER) ?: ""
        val isAlreadyFormatted = intent.getBooleanExtra(EXTRA_IS_ALREADY_FORMATTED, false) == true

        if (text.isEmpty() || vaultUriString.isEmpty()) {
            stopSelf(startId)
            return super.onStartCommand(intent, flags, startId)
        }

        // Start as foreground service
        startForeground(notificationId, createNotification(
            "Processing Speech",
            "Saving transcription to Obsidian..."
        ))

        // Process in background
        serviceScope.launch {
            try {
                // Handle audio file if provided
                if (audioPath != null && audioFileName != null) {
                    updateNotification("Processing Speech", "Saving audio file...")
                    val savedAudioUri = saveAudioToVault(audioPath, audioFileName, vaultUriString, attachmentFolderPath)
                    if (savedAudioUri == null) {
                        updateNotification("Error", "Failed to save audio file")
                        stopSelf(startId)
                        return@launch
                    }
                }

                // Use content directly if it's already formatted, otherwise format it
                val markdownContent = if (isAlreadyFormatted) {
                    text
                } else {
                    formatAsMarkdown(text, audioFileName)
                }

                // Save to Obsidian vault
                updateNotification("Processing Speech", "Saving transcription...")
                val fileUri = saveToObsidianVault(markdownContent, vaultUriString)

                if (fileUri != null) {
                    // Success - update notification
                    updateNotification("Speech Processed", "Transcription saved to Obsidian")

                    // Add link to today's note
                    addLinkToTodayNote(fileUri, vaultUriString)

                    // Refresh widgets
                    WidgetUtils.refreshDailyTodoWidgets(this@SpeechTranscriptionService)

                    // Show completion notification
                    showCompletionNotification(fileUri)

                    val processingTime = System.currentTimeMillis() - startTime
                    AnalyticsHelper.trackSpeechToText(true, processingTime, false)
                } else {
                    updateNotification("Error", "Failed to save transcription")
                }

                // Stop service after a short delay
                Thread.sleep(1000)
                stopSelf(startId)
            } catch (e: Exception) {
                Log.e(TAG, "Error processing speech", e)
                updateNotification("Error", "Failed to process speech: ${e.message}")

                val processingTime = System.currentTimeMillis() - startTime
                AnalyticsHelper.trackSpeechToText(false, processingTime, false)
                AnalyticsHelper.trackError("speech_transcription", e.message ?: "Unknown error")

                stopSelf(startId)
            }
        }

        // Use the parent implementation which returns START_REDELIVER_INTENT
        return super.onStartCommand(intent, flags, startId)
    }

    private fun formatAsMarkdown(text: String, audioFileName: String?): String {
        val sb = StringBuilder()
        val currentDate = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault()).format(Date())

        // Create title from first few words
        val title = if (text.isNotEmpty()) {
            text.take(50).trim()
        } else {
            "Voice Note"
        }

        // Add frontmatter
        sb.append("---\n")
        sb.append("title: \"$title\"\n")
        sb.append("date: $currentDate\n")
        sb.append("tags: [voice, transcription]\n")
        sb.append("---\n\n")

        // Add content
        sb.append("# $title\n\n")

        // Add audio file reference if available
        if (audioFileName != null) {
            sb.append("## Audio Recording\n\n")
            sb.append("![[${audioFileName}]]\n\n")
        }

        // Add transcription
        sb.append("## Transcription\n\n")
        sb.append(text)

        return sb.toString()
    }

    private fun saveAudioToVault(
        audioPath: String,
        audioFileName: String,
        vaultUriString: String,
        attachmentFolderPath: String
    ): Uri? {
        try {
            val vaultUri = vaultUriString.toUri()
            val vaultDoc = DocumentFile.fromTreeUri(this, vaultUri) ?: return null

            // Get the attachment folder path from appDataStore if not provided
            val appDataStore = AppModule.provideAppDataStoreRepository(applicationContext)
            val savedAttachmentFolder = if (attachmentFolderPath.isEmpty()) {
                appDataStore.getStringValue(Constants.Preferences.OBSIDIAN_ATTACHMENT_FOLDER, "")
            } else {
                attachmentFolderPath
            }

            Log.d(TAG, "Original attachment folder path: $savedAttachmentFolder")

            // Navigate to attachment folder or create it if it doesn't exist
            var attachmentDir = vaultDoc

            if (savedAttachmentFolder.isNotEmpty()) {
                // Process the path - if it starts with a dot, it's relative to the vault root
                val processedPath = if (savedAttachmentFolder.startsWith(".")) {
                    // Remove the leading dot and slash if present
                    savedAttachmentFolder.removePrefix("./").removePrefix(".")
                } else {
                    savedAttachmentFolder
                }
                Log.d(TAG, "Processed attachment folder path: $processedPath")

                // Split the path and create each folder
                val folders = processedPath.split("/")
                for (folder in folders) {
                    if (folder.isEmpty()) continue

                    // Find or create each folder in the path
                    val nextFolder = attachmentDir.findFile(folder) ?: attachmentDir.createDirectory(folder)
                    if (nextFolder == null) {
                        Log.e(TAG, "Failed to create folder: $folder")
                        return null
                    }
                    attachmentDir = nextFolder
                }
            } else {
                // Default to "attachments" folder if no specific path is set
                var attachmentsDir = vaultDoc.findFile("attachments")
                if (attachmentsDir == null) {
                    attachmentsDir = vaultDoc.createDirectory("attachments")
                    if (attachmentsDir == null) {
                        Log.e(TAG, "Failed to create attachments folder")
                        return null
                    }
                }
                attachmentDir = attachmentsDir
            }

            // Log the attachment folder path for debugging
            Log.d(TAG, "Using attachment folder: ${attachmentDir.uri}")

            // Create the file in the attachment folder with appropriate MIME type
            val mimeType = when {
                audioFileName.endsWith(".wav") -> "audio/wav"
                audioFileName.endsWith(".m4a") -> "audio/mp4a-latm"
                else -> "audio/*"
            }
            val newFile = attachmentDir.createFile(mimeType, audioFileName) ?: return null

            // Copy the content from the source to the destination
            val audioFile = File(audioPath)
            if (!audioFile.exists()) {
                Log.e(TAG, "Audio file does not exist: $audioPath")
                return null
            }

            audioFile.inputStream().use { inputStream ->
                contentResolver.openOutputStream(newFile.uri)?.use { outputStream ->
                    inputStream.copyTo(outputStream)
                }
            }

            return newFile.uri
        } catch (e: Exception) {
            Log.e(TAG, "Error saving audio to vault", e)
            return null
        }
    }

    private fun saveToObsidianVault(content: String, vaultUriString: String): Uri? {
        try {
            val vaultUri = vaultUriString.toUri()
            val vaultDoc = DocumentFile.fromTreeUri(this, vaultUri) ?: return null

            // Create Clippings folder if needed
            var clippingsFolder = vaultDoc.findFile("Clippings")
            if (clippingsFolder == null) {
                clippingsFolder = vaultDoc.createDirectory("Clippings")
                if (clippingsFolder == null) {
                    Log.e(TAG, "Failed to create Clippings folder")
                    return null
                }
            }

            // Create filename with timestamp
            val timestamp = SimpleDateFormat("yyyy-MM-dd-HHmmss", Locale.US).format(Date())
            val fileName = "Voice-$timestamp.md"

            // Create file
            val file = clippingsFolder.createFile("text/markdown", fileName)
            if (file == null) {
                Log.e(TAG, "Failed to create file")
                return null
            }

            // Write content
            contentResolver.openOutputStream(file.uri)?.use { outputStream ->
                outputStream.write(content.toByteArray())
            }

            return file.uri
        } catch (e: Exception) {
            Log.e(TAG, "Error saving to Obsidian vault", e)
            return null
        }
    }

    private fun addLinkToTodayNote(fileUri: Uri, vaultUriString: String) {
        // Extract a title from the file URI
        val fileName = fileUri.lastPathSegment?.substringAfterLast('/') ?: "Voice Note"
        val title = fileName.substringBeforeLast('.').replace("-", " ")

        // Start the TodayNoteService to add the link
        TodayNoteService.startLinkService(
            context = this,
            fileUri = fileUri.toString(),
            fileTitle = title,
            vaultUri = vaultUriString,
            fileType = "voice"
        )
    }

    private fun showCompletionNotification(fileUri: Uri) {
        // Create a notification to show that the transcription is complete
        val notificationManager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager

        val notificationBuilder = NotificationCompat.Builder(this, NOTIFICATION_CHANNEL_ID)
            .setContentTitle("Voice Transcription Saved")
            .setContentText("Tap to view in Obsidian")
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setAutoCancel(true)

        // Create an intent to open the file in Obsidian
        val openIntent = Intent(Intent.ACTION_VIEW).apply {
            data = fileUri
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
        }

        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            openIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        notificationBuilder.setContentIntent(pendingIntent)

        // Show the notification
        notificationManager.notify(notificationId + 1000, notificationBuilder.build())
    }
}
