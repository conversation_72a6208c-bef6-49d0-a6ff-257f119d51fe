## This file must *NOT* be checked into Version Control Systems,
# as it contains information specific to your local configuration.
#
# Location of the SDK. This is only used by Gradle.
# For customization when using a Version Control System, please read the
# header note.
sdk.dir=C\:\\Users\\YourUsername\\AppData\\Local\\Android\\Sdk

# Signing configuration for release builds
# Copy these lines to your actual local.properties file and fill in the values
# KEYSTORE_PATH=path/to/your/keystore.jks
# KEYSTORE_PASSWORD=your-keystore-password
# KEY_ALIAS=your-key-alias
# KEY_PASSWORD=your-key-password

# Example:
# KEYSTORE_PATH=../signing/vaultsnap-release.jks
# KEYSTORE_PASSWORD=your-secure-password
# KEY_ALIAS=vaultsnap
# KEY_PASSWORD=your-key-password
