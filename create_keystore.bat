@echo off
REM Script to create a release keystore for keepsidian

echo 🔐 Creating release keystore for keepsidian...
echo.

REM Create signing directory if it doesn't exist
if not exist "signing" mkdir signing

echo This will create a keystore file for signing your release APKs.
echo Please provide the following information:
echo.

set /p STORE_PASSWORD="Enter keystore password (remember this!): "
set /p KEY_PASSWORD="Enter key password (can be same as keystore password): "
set /p ALIAS_NAME="Enter key alias (e.g., keepsidian): "
set /p YOUR_NAME="Enter your name: "
set /p ORG_UNIT="Enter organizational unit (e.g., Development): "
set /p ORGANIZATION="Enter organization name: "
set /p CITY="Enter city: "
set /p STATE="Enter state/province: "
set /p COUNTRY="Enter country code (e.g., US): "

echo.
echo Creating keystore with the following details:
echo - Keystore: signing\keepsidian-release.jks
echo - Alias: %ALIAS_NAME%
echo - Validity: 25 years
echo.

keytool -genkey -v ^
    -keystore signing\keepsidian-release.jks ^
    -keyalg RSA ^
    -keysize 2048 ^
    -validity 9125 ^
    -alias %ALIAS_NAME% ^
    -storepass %STORE_PASSWORD% ^
    -keypass %KEY_PASSWORD% ^
    -dname "CN=%YOUR_NAME%, OU=%ORG_UNIT%, O=%ORGANIZATION%, L=%CITY%, ST=%STATE%, C=%COUNTRY%"

if %errorlevel% equ 0 (
    echo.
    echo ✅ Keystore created successfully!
    echo.
    echo 📝 Add these lines to your local.properties file:
    echo KEYSTORE_PATH=signing/keepsidian-release.jks
    echo KEYSTORE_PASSWORD=%STORE_PASSWORD%
    echo KEY_ALIAS=%ALIAS_NAME%
    echo KEY_PASSWORD=%KEY_PASSWORD%
    echo.
    echo ⚠️  IMPORTANT: Keep these credentials safe and never commit them to version control!
    echo.
    echo Would you like me to add these to local.properties automatically? (y/n)
    set /p ADD_TO_LOCAL="Enter choice: "
    
    if /i "%ADD_TO_LOCAL%"=="y" (
        echo. >> local.properties
        echo # Signing configuration for release builds >> local.properties
        echo KEYSTORE_PATH=signing/keepsidian-release.jks >> local.properties
        echo KEYSTORE_PASSWORD=%STORE_PASSWORD% >> local.properties
        echo KEY_ALIAS=%ALIAS_NAME% >> local.properties
        echo KEY_PASSWORD=%KEY_PASSWORD% >> local.properties
        echo.
        echo ✅ Configuration added to local.properties
    )
) else (
    echo.
    echo ❌ Failed to create keystore. Please check the error messages above.
)

echo.
echo Press any key to exit...
pause >nul
