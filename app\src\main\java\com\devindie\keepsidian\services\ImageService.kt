package com.devindie.keepsidian.services

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.IBinder
import android.util.Log
import androidx.core.net.toUri
import androidx.documentfile.provider.DocumentFile
import com.devindie.keepsidian.AppModule
import com.devindie.keepsidian.Constants
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * Service for saving images to Obsidian vault without OCR processing
 */
class ImageService : BaseProcessingService() {
    companion object {
        private const val TAG = "ImageService"
        private const val NOTIFICATION_ID = 1003

        const val EXTRA_IMAGE_URI = "com.devindie.keepsidian.extra.IMAGE_URI"
        const val EXTRA_VAULT_URI = "com.devindie.keepsidian.extra.VAULT_URI"
        const val EXTRA_ATTACHMENT_FOLDER = "com.devindie.keepsidian.extra.ATTACHMENT_FOLDER"

        fun startService(context: Context, imageUri: String, vaultUri: String, attachmentFolder: String) {
            val intent = Intent(context, ImageService::class.java).apply {
                putExtra(EXTRA_IMAGE_URI, imageUri)
                putExtra(EXTRA_VAULT_URI, vaultUri)
                putExtra(EXTRA_ATTACHMENT_FOLDER, attachmentFolder)
            }

            context.startForegroundService(intent)
        }
    }

    override val serviceName: String = TAG
    override val notificationId: Int = NOTIFICATION_ID

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    override fun onStartCommand(intent: Intent, flags: Int, startId: Int): Int {
        val imageUriString = intent.getStringExtra(EXTRA_IMAGE_URI) ?: ""
        val vaultUriString = intent.getStringExtra(EXTRA_VAULT_URI) ?: ""
        val attachmentFolderPath = intent.getStringExtra(EXTRA_ATTACHMENT_FOLDER) ?: ""

        if (imageUriString.isEmpty() || vaultUriString.isEmpty()) {
            stopSelf(startId)
            return super.onStartCommand(intent, flags, startId)
        }

        // Start as foreground service
        startForeground(notificationId, createNotification(
            "Saving Image",
            "Saving image to Obsidian vault..."
        ))

        // Process in background
        serviceScope.launch {
            try {
                val imageUri = Uri.parse(imageUriString)

                // Step 1: Save the image to the vault's attachment folder
                updateNotification("Saving Image", "Saving to vault...")
                val savedImageUri = saveImageToVault(imageUri, vaultUriString, attachmentFolderPath)
                if (savedImageUri == null) {
                    updateNotification("Error", "Failed to save image to vault")
                    stopSelf(startId)
                    return@launch
                }

                // Get the relative path of the saved image for embedding in markdown
                getRelativePathInVault(savedImageUri, vaultUriString)

                // Get the file name for the title
                val fileName = savedImageUri.lastPathSegment?.substringAfterLast('/') ?: "Image"
                val title = "Image: ${fileName.substringBeforeLast('.')}"

                // Step 2: Add link to today's note
                updateNotification("Saving Image", "Adding to today's note...")
                addLinkToTodayNote(savedImageUri, title, vaultUriString)

                // Success - update notification
                updateNotification("Image Saved", "Image saved to Obsidian vault")

                // Stop service after a short delay
                Thread.sleep(1000)
                stopSelf(startId)
            } catch (e: Exception) {
                Log.e(TAG, "Error saving image", e)
                updateNotification("Error", "Failed to save image: ${e.message}")
                stopSelf(startId)
            }
        }

        // Use the parent implementation which returns START_REDELIVER_INTENT
        return super.onStartCommand(intent, flags, startId)
    }

    private fun saveImageToVault(imageUri: Uri, vaultUriString: String, attachmentFolderPath: String): Uri? {
        try {
            val vaultUri = vaultUriString.toUri()
            val vaultDoc = DocumentFile.fromTreeUri(this, vaultUri) ?: return null

            // Get the attachment folder path from appDataStore if not provided
            val appDataStore = AppModule.provideAppDataStoreRepository(applicationContext)
            val savedAttachmentFolder = if (attachmentFolderPath.isEmpty()) {
                appDataStore.getStringValue(Constants.Preferences.OBSIDIAN_ATTACHMENT_FOLDER, "")
            } else {
                attachmentFolderPath
            }

            Log.d(TAG, "Original attachment folder path: $savedAttachmentFolder")

            // Navigate to attachment folder or create it if it doesn't exist
            var attachmentDir = vaultDoc

            if (savedAttachmentFolder.isNotEmpty()) {
                // Process the path - if it starts with a dot, it's relative to the vault root
                val processedPath = if (savedAttachmentFolder.startsWith(".")) {
                    // Remove the leading dot and slash if present
                    savedAttachmentFolder.removePrefix("./").removePrefix(".")
                } else {
                    savedAttachmentFolder
                }
                Log.d(TAG, "Processed attachment folder path: $processedPath")

                // Split the path and create each folder
                val folders = processedPath.split("/")
                for (folder in folders) {
                    if (folder.isEmpty()) continue

                    // Find or create each folder in the path
                    val nextFolder = attachmentDir.findFile(folder) ?: attachmentDir.createDirectory(folder)
                    if (nextFolder == null) {
                        Log.e(TAG, "Failed to create folder: $folder")
                        return null
                    }
                    attachmentDir = nextFolder
                }
            } else {
                // Default to "attachments" folder if no specific path is set
                var attachmentsDir = vaultDoc.findFile("attachments")
                if (attachmentsDir == null) {
                    attachmentsDir = vaultDoc.createDirectory("attachments")
                    if (attachmentsDir == null) {
                        Log.e(TAG, "Failed to create attachments folder")
                        return null
                    }
                }
                attachmentDir = attachmentsDir
            }

            // Create a filename based on current date/time
            val timestamp = SimpleDateFormat("yyyyMMdd-HHmmss", Locale.US).format(Date())
            val extension = imageUri.lastPathSegment?.substringAfterLast('.', "jpg") ?: "jpg"
            val fileName = "image-$timestamp.$extension"

            // Log the attachment folder path for debugging
            Log.d(TAG, "Using attachment folder: ${attachmentDir.uri}")

            // Create the file in the attachment folder
            val newFile = attachmentDir.createFile("image/*", fileName) ?: return null

            // Copy the content from the source to the destination
            contentResolver.openInputStream(imageUri)?.use { inputStream ->
                contentResolver.openOutputStream(newFile.uri)?.use { outputStream ->
                    inputStream.copyTo(outputStream)
                }
            }

            return newFile.uri
        } catch (e: Exception) {
            Log.e(TAG, "Error saving image to vault", e)
            return null
        }
    }

    private fun getRelativePathInVault(fileUri: Uri, vaultUriString: String): String {
        try {
            // Get the save location from preferences
            val appDataStore = AppModule.provideAppDataStoreRepository(applicationContext)

            // Get the file name
            val fileName = fileUri.lastPathSegment?.substringAfterLast('/') ?: "image.jpg"

            // Image files are saved in the attachment folder
            val attachmentFolder = appDataStore.getStringValue(Constants.Preferences.OBSIDIAN_ATTACHMENT_FOLDER, "")
            if (attachmentFolder.isNotEmpty()) {
                // Process the path - if it starts with a dot, it's relative to the vault root
                val processedPath = if (attachmentFolder.startsWith(".")) {
                    // Remove the leading dot and slash if present
                    attachmentFolder.removePrefix("./").removePrefix(".")
                } else {
                    attachmentFolder
                }
                Log.d(TAG, "Using path: $processedPath/$fileName")
                return "$processedPath/$fileName"
            } else {
                // Default to "attachments" folder if no specific path is set
                Log.d(TAG, "Using default path: attachments/$fileName")
                return "attachments/$fileName"
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting relative path", e)
            return ""
        }
    }

    private fun addLinkToTodayNote(fileUri: Uri, title: String, vaultUriString: String) {
        // Start the TodayNoteService to add the link
        TodayNoteService.startLinkService(
            context = this,
            fileUri = fileUri.toString(),
            fileTitle = title,
            vaultUri = vaultUriString,
            fileType = "image"
        )
    }
}
