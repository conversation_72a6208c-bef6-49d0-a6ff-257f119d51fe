package com.devindie.keepsidian

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.media.AudioFormat
import android.media.MediaRecorder
import android.os.Bundle
import android.speech.RecognitionListener
import android.speech.SpeechRecognizer
import android.speech.RecognizerIntent
import android.util.Log
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Mic
import androidx.compose.material.icons.filled.Stop
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.compose.ui.res.stringResource
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.devindie.keepsidian.services.SpeechTranscriptionService
import com.devindie.keepsidian.services.VoskTranscriptionService
import com.github.squti.androidwaverecorder.RecorderState
import com.github.squti.androidwaverecorder.WaveRecorder
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class SpeechRecordingActivity : ComponentActivity() {
    companion object {
        private const val TAG = "SpeechRecordingActivity"
        private const val REQUEST_RECORD_AUDIO_PERMISSION = 200
    }

    private lateinit var speechRecognizer: SpeechRecognizer
    private var isListening = false
    private var transcribedText = ""
    private var displayText = mutableStateOf("") // For UI display including partial results

    // Initialize with some default values to ensure we have bars to display
    private var audioLevels =
        mutableStateListOf(0.1f, 0.1f, 0.1f, 0.1f, 0.1f, 0.1f, 0.1f, 0.1f, 0.1f, 0.1f)
    private var recordingTimer: Job? = null
    private var remainingTimeSeconds = Constants.Preferences.MAX_RECORDING_TIME_SECONDS

    // Audio recording
    private var mediaRecorder: MediaRecorder? = null
    private var waveRecorder: WaveRecorder? = null
    private var audioFilePath: String? = null
    private var isRecording = false
    private var audioLevelMonitorJob: Job? = null
    private var lastRmsUpdateTime = 0L

    // Offline recognition
    private var useOfflineRecognition = false

    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            initializeSpeechRecognizer()
        } else {
            Toast.makeText(this, getString(R.string.permission_denied), Toast.LENGTH_SHORT).show()
            finish()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Check if Speech-To-Text is enabled
        val appDataStore = AppModule.provideAppDataStoreRepository(applicationContext)
        val isSpeechToTextEnabled =
            appDataStore.getBooleanValue(Constants.Preferences.ENABLE_SPEECH_TO_TEXT, false)

        if (!isSpeechToTextEnabled) {
            Toast.makeText(
                this,
                getString(R.string.speech_to_text_disabled),
                Toast.LENGTH_SHORT
            ).show()
            finish()
            return
        }

        // Check if offline recognition is enabled
        useOfflineRecognition = appDataStore.getBooleanValue(
            Constants.Preferences.ENABLE_OFFLINE_SPEECH_RECOGNITION,
            false
        )

        // Check for permission
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO) !=
            PackageManager.PERMISSION_GRANTED
        ) {
            requestPermissionLauncher.launch(Manifest.permission.RECORD_AUDIO)
        } else {
            initializeSpeechRecognizer()
        }

        setContent {
            RecordingDialog()
        }
    }

    private fun initializeSpeechRecognizer() {
        // Reset audio levels when starting a new recording
        audioLevels.clear()
        // Initialize with some default values
        repeat(10) {
            audioLevels.add(0.1f)
        }

        speechRecognizer = SpeechRecognizer.createSpeechRecognizer(this)
        speechRecognizer.setRecognitionListener(createRecognitionListener())
        startListening()
        startRecordingTimer()
        startAudioRecording()
    }

    private fun startAudioRecording() {
        try {
            // Get vault URI and attachment folder path
            val appDataStore = AppModule.provideAppDataStoreRepository(applicationContext)
            val vaultUriString = appDataStore.getStringValue(Constants.Preferences.OBSIDIAN_VAULT_URI, "")
            appDataStore.getStringValue(Constants.Preferences.OBSIDIAN_ATTACHMENT_FOLDER, "")

            if (vaultUriString.isEmpty()) {
                Log.e(TAG, "Obsidian vault not configured")
                Toast.makeText(this, getString(R.string.vault_not_configured), Toast.LENGTH_SHORT).show()
                return
            }

            // Create a temporary file for recording
            val tempDir = getExternalFilesDir(null)

            if (useOfflineRecognition) {
                // Use WaveRecorder for WAV files when offline recognition is enabled
                val tempFile = File(tempDir, "temp_recording.wav")
                audioFilePath = tempFile.absolutePath

                // Initialize WaveRecorder
                waveRecorder = WaveRecorder(audioFilePath!!)

                // Configure WaveRecorder for Vosk compatibility (16kHz mono)
                waveRecorder?.configureWaveSettings {
                    sampleRate = 16000
                    channels = AudioFormat.CHANNEL_IN_MONO
                    audioEncoding = AudioFormat.ENCODING_PCM_16BIT
                }

                // Set up amplitude listener for visualization
                waveRecorder?.onAmplitudeListener = { amplitude ->
                    // Normalize amplitude to 0-1 range for visualization
                    val normalizedLevel = (amplitude / 32767f).coerceIn(0f, 1f)
                    updateAudioLevels(normalizedLevel)
                    Log.d(TAG, "WaveRecorder amplitude: $amplitude, Normalized: $normalizedLevel")
                    lastRmsUpdateTime = System.currentTimeMillis()
                }

                // Set up state change listener
                waveRecorder?.onStateChangeListener = { state ->
                    when (state) {
                        RecorderState.RECORDING -> Log.d(TAG, "WaveRecorder state: RECORDING")
                        RecorderState.STOP -> Log.d(TAG, "WaveRecorder state: STOP")
                        RecorderState.PAUSE -> Log.d(TAG, "WaveRecorder state: PAUSE")
                        RecorderState.SKIPPING_SILENCE -> Log.d(TAG, "WaveRecorder state: SKIPPING_SILENCE")
                    }
                }

                // Start recording
                waveRecorder?.startRecording()

                isRecording = true
                Log.d(TAG, "Started WAV recording to: $audioFilePath")
            } else {
                // Use MediaRecorder for non-offline recording (M4A format)
                val tempFile = File(tempDir, "temp_recording.m4a")
                audioFilePath = tempFile.absolutePath

                // Initialize MediaRecorder
                mediaRecorder =
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                        MediaRecorder(this)
                    } else {
                        @Suppress("DEPRECATION")
                        MediaRecorder()
                    }

                mediaRecorder?.apply {
                    setAudioSource(MediaRecorder.AudioSource.MIC)
                    setOutputFormat(MediaRecorder.OutputFormat.MPEG_4)
                    setAudioEncoder(MediaRecorder.AudioEncoder.AAC)
                    setOutputFile(audioFilePath)
                    prepare()
                    start()
                }

                isRecording = true
                Log.d(TAG, "Started M4A recording to: $audioFilePath")

                // Start monitoring audio levels from MediaRecorder
                startAudioLevelMonitoring()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error starting audio recording", e)
            Toast.makeText(this, getString(R.string.failed_to_start_audio_recording), Toast.LENGTH_SHORT).show()
        }
    }

    private fun startRecordingTimer() {
        // Cancel any existing timer
        recordingTimer?.cancel()

        // Reset the timer
        remainingTimeSeconds = Constants.Preferences.MAX_RECORDING_TIME_SECONDS

        // Start a new timer
        recordingTimer = lifecycleScope.launch {
            while (remainingTimeSeconds > 0 && isListening) {
                delay(1000) // Wait for 1 second
                remainingTimeSeconds--

                // If time is up, stop recording and save automatically
                if (remainingTimeSeconds <= 0) {
                    stopListening()
                    saveTranscription()
                }
            }
        }
    }

    private fun startListening() {
        val intent = Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH).apply {
            putExtra(
                RecognizerIntent.EXTRA_LANGUAGE_MODEL,
                RecognizerIntent.LANGUAGE_MODEL_FREE_FORM
            )
            putExtra(RecognizerIntent.EXTRA_PARTIAL_RESULTS, true)
            putExtra(RecognizerIntent.EXTRA_MAX_RESULTS, 5)
            putExtra(RecognizerIntent.EXTRA_SPEECH_INPUT_MINIMUM_LENGTH_MILLIS, 500)
            putExtra(RecognizerIntent.EXTRA_SPEECH_INPUT_COMPLETE_SILENCE_LENGTH_MILLIS, 500)
            putExtra(
                RecognizerIntent.EXTRA_SPEECH_INPUT_POSSIBLY_COMPLETE_SILENCE_LENGTH_MILLIS,
                500
            )
            putExtra(RecognizerIntent.EXTRA_PREFER_OFFLINE, false)
        }

        try {
            isListening = true
            speechRecognizer.startListening(intent)
        } catch (e: Exception) {
            Log.e(TAG, "Error starting speech recognition", e)
            Toast.makeText(this, getString(R.string.speech_recognition_failed), Toast.LENGTH_SHORT).show()
            finish()
        }
    }

    private fun createRecognitionListener(): RecognitionListener {
        return object : RecognitionListener {
            private var partialText = ""

            override fun onReadyForSpeech(params: Bundle?) {
                Log.d(TAG, "Ready for speech")
            }

            override fun onBeginningOfSpeech() {
                Log.d(TAG, "Beginning of speech")
            }

            override fun onRmsChanged(rmsdB: Float) {
                // Update audio level visualization
                // Android's RMS values typically range from -100 to 0, with -100 being silence and 0 being loudest
                // But in practice, they often range from about -20 to 0

                // Better normalization for typical Android RMS values
                val normalizedLevel = ((rmsdB + 20) / 20).coerceIn(0f, 1f)

                // Update the timestamp of the last RMS update
                lastRmsUpdateTime = System.currentTimeMillis()

                // Use our common function to update audio levels
                updateAudioLevels(normalizedLevel)

                Log.d(TAG, "SpeechRecognizer RMS: $rmsdB, Normalized: $normalizedLevel")
            }

            override fun onBufferReceived(buffer: ByteArray?) {}

            override fun onEndOfSpeech() {
                Log.d(TAG, "End of speech")
                // Save partial text if we have it but no final results
                if (partialText.isNotEmpty() && isListening) {
                    if (transcribedText.isNotEmpty()) {
                        transcribedText += " "
                    }
                    transcribedText += partialText
                    partialText = ""
                }

                // Restart listening for continuous recognition
                if (isListening) {
                    startListening()
                }
            }

            override fun onError(error: Int) {
                val errorMessage = when (error) {
                    SpeechRecognizer.ERROR_AUDIO -> getString(R.string.error_audio_recording)
                    SpeechRecognizer.ERROR_CLIENT -> getString(R.string.error_client)
                    SpeechRecognizer.ERROR_INSUFFICIENT_PERMISSIONS -> getString(R.string.error_insufficient_permissions)
                    SpeechRecognizer.ERROR_NETWORK -> getString(R.string.error_network)
                    SpeechRecognizer.ERROR_NETWORK_TIMEOUT -> getString(R.string.error_network_timeout)
                    SpeechRecognizer.ERROR_NO_MATCH -> getString(R.string.error_no_match)
                    SpeechRecognizer.ERROR_RECOGNIZER_BUSY -> getString(R.string.error_recognizer_busy)
                    SpeechRecognizer.ERROR_SERVER -> getString(R.string.error_server)
                    SpeechRecognizer.ERROR_SPEECH_TIMEOUT -> getString(R.string.error_speech_timeout)
                    else -> getString(R.string.error_unknown)
                }
                Log.e(TAG, "Error in speech recognition: $error - $errorMessage")

                // Save partial text if we have it
                if (partialText.isNotEmpty()) {
                    if (transcribedText.isNotEmpty()) {
                        transcribedText += " "
                    }
                    transcribedText += partialText
                    partialText = ""
                }

                // Restart listening unless it was a "no match" error or speech timeout
                if (error != SpeechRecognizer.ERROR_NO_MATCH &&
                    error != SpeechRecognizer.ERROR_SPEECH_TIMEOUT &&
                    isListening
                ) {
                    startListening()
                }
            }

            override fun onResults(results: Bundle?) {
                val matches = results?.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION)
                if (!matches.isNullOrEmpty()) {
                    val result = matches[0]
                    Log.d(TAG, "Final result: $result")

                    // Append to existing text with space
                    if (transcribedText.isNotEmpty()) {
                        transcribedText += " "
                    }
                    transcribedText += result
                    partialText = "" // Clear partial text

                    // Restart listening for continuous recognition
                    if (isListening) {
                        startListening()
                    }
                }
            }

            override fun onPartialResults(partialResults: Bundle?) {
                val matches =
                    partialResults?.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION)
                if (!matches.isNullOrEmpty()) {
                    val result = matches[0]
                    Log.d(TAG, "Partial result: $result")

                    // Save the partial result
                    partialText = result

                    // Update UI with combined text (transcribed + partial)
                    val combinedText = if (transcribedText.isEmpty()) {
                        partialText
                    } else {
                        "$transcribedText $partialText"
                    }

                    // Update the display text for the UI
                    displayText.value = combinedText
                    Log.d(TAG, "Updating UI with: ${displayText.value}")
                }
            }

            override fun onEvent(eventType: Int, params: Bundle?) {}
        }
    }

    private fun stopListening() {
        isListening = false
        recordingTimer?.cancel()
        try {
            speechRecognizer.stopListening()
            speechRecognizer.destroy()
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping speech recognizer", e)
        }
        stopAudioRecording()

        // Update UI to show recording has stopped
        displayText.value = transcribedText
    }

    private fun startAudioLevelMonitoring() {
        // Cancel any existing job
        audioLevelMonitorJob?.cancel()

        // Start a new job to monitor audio levels
        audioLevelMonitorJob = lifecycleScope.launch {
            while (isActive && isRecording) {
                try {
                    // Only update from MediaRecorder if we haven't received an RMS update recently
                    val currentTime = System.currentTimeMillis()
                    if (currentTime - lastRmsUpdateTime > 100) { // If no RMS update in last 100ms
                        mediaRecorder?.let { recorder ->
                            // getMaxAmplitude returns a value between 0 and 32767
                            val amplitude = recorder.maxAmplitude.toFloat()

                            // Normalize to 0-1 range (using empirical values)
                            // 0 is silence, 32767 is maximum
                            val normalizedLevel = (amplitude / 32767f).coerceIn(0f, 1f)

                            // Only update if the amplitude is significant
                            if (amplitude > 100) {
                                updateAudioLevels(normalizedLevel)
                                Log.d(
                                    TAG,
                                    "MediaRecorder amplitude: $amplitude, Normalized: $normalizedLevel"
                                )
                            }
                        }
                    }
                    delay(50) // Check every 50ms
                } catch (e: Exception) {
                    Log.e(TAG, "Error getting max amplitude", e)
                    delay(500) // Wait longer if there's an error
                }
            }
        }
    }

    private fun updateAudioLevels(level: Float) {
        // Update the audio levels list - remove first, add to end to create animation effect
        if (audioLevels.size >= 10) {
            audioLevels.removeAt(0)
        }
        audioLevels.add(level)

        // Ensure we always have 10 bars
        while (audioLevels.size < 10) {
            audioLevels.add(0.1f)
        }
    }

    private fun stopAudioRecording() {
        if (isRecording) {
            try {
                // Cancel the audio level monitoring job
                audioLevelMonitorJob?.cancel()
                audioLevelMonitorJob = null

                if (useOfflineRecognition) {
                    // Stop WaveRecorder
                    waveRecorder?.stopRecording()
                    waveRecorder = null
                } else {
                    // Stop MediaRecorder
                    mediaRecorder?.apply {
                        stop()
                        release()
                    }
                    mediaRecorder = null
                }

                isRecording = false
                Log.d(TAG, "Stopped audio recording")
            } catch (e: Exception) {
                Log.e(TAG, "Error stopping audio recording", e)
            }
        }
    }

    private fun saveTranscription() {
        // Get vault URI
        val appDataStore = AppModule.provideAppDataStoreRepository(applicationContext)
        val vaultUriString = appDataStore.getStringValue(Constants.Preferences.OBSIDIAN_VAULT_URI, "")
        val attachmentFolderPath = appDataStore.getStringValue(Constants.Preferences.OBSIDIAN_ATTACHMENT_FOLDER, "")
        appDataStore.getStringValue(Constants.Preferences.OBSIDIAN_SAVE_LOCATION, "Clippings")

        if (vaultUriString.isEmpty()) {
            Toast.makeText(this, getString(R.string.vault_not_configured), Toast.LENGTH_SHORT).show()
            finish()
            return
        }

        // Check if we have transcribed text or audio
        if (transcribedText.isEmpty() && audioFilePath == null) {
            Toast.makeText(this, getString(R.string.no_speech_detected), Toast.LENGTH_SHORT)
                .show()
            finish()
            return
        }

        // If no speech was detected but we have audio, use a default message
        val textToSave = transcribedText.ifEmpty {
            getString(R.string.no_speech_placeholder)
        }

        // If we have an audio file, save it along with the transcription
        if (audioFilePath != null) {
            // Generate a predictable audio filename based on timestamp
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val audioFileName = if (useOfflineRecognition) {
                "voice_recording_$timestamp.wav"
            } else {
                "voice_recording_$timestamp.m4a"
            }

            if (useOfflineRecognition) {
                // Use Vosk for offline transcription
                Toast.makeText(
                    this,
                    getString(R.string.use_offline_recognition),
                    Toast.LENGTH_SHORT
                ).show()

                // Start the VoskTranscriptionService for offline transcription
                VoskTranscriptionService.startService(
                    context = this,
                    audioPath = audioFilePath!!,
                    vaultUri = vaultUriString,
                    attachmentFolder = attachmentFolderPath
                )

                Log.d(TAG, "Started VoskTranscriptionService for offline transcription with WAV file")
            } else {
                // Create markdown content with audio reference
                val markdownContent = formatAsMarkdownWithAudio(textToSave, audioFileName)

                // Start the SpeechTranscriptionService to handle both audio and transcription
                SpeechTranscriptionService.startService(
                    context = this,
                    text = markdownContent,
                    audioPath = audioFilePath,
                    audioFileName = audioFileName,
                    vaultUri = vaultUriString,
                    attachmentFolder = attachmentFolderPath,
                    isAlreadyFormatted = true
                )

                Log.d(TAG, "Started SpeechTranscriptionService with audio file")
            }
        } else {
            // No audio, just save the transcription
            val markdownContent = formatAsMarkdown(textToSave)

            // Start the SpeechTranscriptionService for transcription only
            SpeechTranscriptionService.startService(
                context = this,
                text = markdownContent,
                vaultUri = vaultUriString,
                attachmentFolder = attachmentFolderPath,
                isAlreadyFormatted = true
            )

            Log.d(TAG, "Started SpeechTranscriptionService for transcription only")
        }

        finish()
    }


    private fun formatAsMarkdownWithAudio(text: String, audioFileName: String): String {
        val sb = StringBuilder()
        val currentDate = SimpleDateFormat("yyyy-MM-dd", Locale.US).format(Date())

        // Create title from first few words
        val title = if (text.isNotEmpty()) {
            text.take(50).trim()
        } else {
            getString(R.string.voice_note)
        }

        // Add frontmatter
        sb.append("---\n")
        sb.append("${getString(R.string.markdown_title)}: \"$title\"\n")
        sb.append("${getString(R.string.markdown_date)}: $currentDate\n")
        sb.append("${getString(R.string.markdown_tags)}: ${getString(R.string.tags_voice_transcription)}\n")
        sb.append("---\n\n")

        // Add content
        sb.append("# $title\n\n")

        // Add audio file reference
        sb.append("## ${getString(R.string.audio_recording)}\n\n")
        sb.append("![[${audioFileName}]]\n\n")

        // Add transcription
        sb.append("## ${getString(R.string.transcription)}\n\n")
        sb.append(text)

        return sb.toString()
    }

    private fun formatAsMarkdown(text: String): String {
        val sb = StringBuilder()
        val currentDate = SimpleDateFormat("yyyy-MM-dd", Locale.US).format(Date())

        // Create title from first few words
        val title = if (text.isNotEmpty()) {
            text.take(50).trim()
        } else {
            getString(R.string.voice_note)
        }

        // Add frontmatter
        sb.append("---\n")
        sb.append("${getString(R.string.markdown_title)}: \"$title\"\n")
        sb.append("${getString(R.string.markdown_date)}: $currentDate\n")
        sb.append("${getString(R.string.markdown_tags)}: ${getString(R.string.tags_voice_transcription)}\n")
        sb.append("---\n\n")

        // Add content
        sb.append("# $title\n\n")
        sb.append("## ${getString(R.string.transcription)}\n\n")
        sb.append(text)

        return sb.toString()
    }

    override fun onDestroy() {
        super.onDestroy()
        recordingTimer?.cancel()
        audioLevelMonitorJob?.cancel()
        stopAudioRecording()
        if (::speechRecognizer.isInitialized) {
            speechRecognizer.destroy()
        }
        // Ensure WaveRecorder is properly stopped
        waveRecorder?.stopRecording()
        waveRecorder = null
    }

    @Composable
    fun RecordingDialog() {
        var showDialog by remember { mutableStateOf(true) }
        var isRecordingState by remember { mutableStateOf(true) }
        var currentText by remember { mutableStateOf("") }
        var timeRemaining by remember { mutableStateOf(Constants.Preferences.MAX_RECORDING_TIME_SECONDS) }
        var isOfflineMode by remember { mutableStateOf(useOfflineRecognition) }

        // Update recording state when isListening changes
        LaunchedEffect(isListening) {
            isRecordingState = isListening
        }

        // Progress for the countdown timer
        val progress by animateFloatAsState(
            targetValue = timeRemaining.toFloat() / Constants.Preferences.MAX_RECORDING_TIME_SECONDS.toFloat(),
            label = "timerProgress"
        )

        // Update the displayed text from the displayText state
        LaunchedEffect(displayText.value) {
            currentText = displayText.value
        }

        // Update the time remaining with a continuous polling
        LaunchedEffect(Unit) {
            while (true) {
                timeRemaining = remainingTimeSeconds
                delay(100) // Poll every 100ms for smoother updates
            }
        }

        if (showDialog) {
            Dialog(
                onDismissRequest = {
                    stopListening()
                    showDialog = false
                    finish()
                },
                properties = DialogProperties(
                    dismissOnBackPress = true,
                    dismissOnClickOutside = true,
                    usePlatformDefaultWidth = false
                )
            ) {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.9f)
                    )
                ) {
                    Column(
                        modifier = Modifier
                            .padding(16.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        // Title and timer
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Column {
                                Text(
                                    text = stringResource(R.string.voice_recording),
                                    style = MaterialTheme.typography.titleLarge
                                )

                                // Show offline mode indicator if enabled
                                if (isOfflineMode) {
                                    Text(
                                        text = stringResource(R.string.offline_speech_recognition),
                                        style = MaterialTheme.typography.bodySmall,
                                        color = MaterialTheme.colorScheme.secondary
                                    )
                                }
                            }

                            // Timer display
                            if (isRecording) {
                                Text(
                                    text = stringResource(R.string.seconds_remaining, timeRemaining),
                                    style = MaterialTheme.typography.titleMedium,
                                    color = if (timeRemaining <= 3) Color.Red else MaterialTheme.colorScheme.primary,
                                    fontWeight = FontWeight.Bold
                                )
                            }
                        }

                        // Max recording time info
                        Text(
                            text = stringResource(R.string.max_recording_time_info, Constants.Preferences.MAX_RECORDING_TIME_SECONDS),
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f),
                            modifier = Modifier.padding(bottom = 16.dp)
                        )

                        // Progress indicator
                        LinearProgressIndicator(
                            progress = { progress },
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(8.dp)
                                .clip(RoundedCornerShape(4.dp)),
                            color = if (timeRemaining <= 3) Color.Red else MaterialTheme.colorScheme.primary,
                            trackColor = MaterialTheme.colorScheme.surfaceVariant
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        // Transcribed text area
                        Surface(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(150.dp)
                                .padding(bottom = 16.dp),
                            shape = RoundedCornerShape(8.dp),
                            color = MaterialTheme.colorScheme.surfaceVariant
                        ) {
                            Box(
                                contentAlignment = Alignment.Center,
                                modifier = Modifier
                                    .padding(8.dp)
                                    .wrapContentHeight()
                            ) {
                                Text(
                                    text = currentText.ifEmpty { stringResource(R.string.speak_now) },
                                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                                    modifier = Modifier
                                        .fillMaxSize()
                                        .padding(8.dp),
                                    textAlign = if (currentText.isEmpty()) TextAlign.Center else TextAlign.Start
                                )

                                // Audio level visualization
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .height(60.dp)
                                        .padding(bottom = 16.dp),
                                    horizontalArrangement = Arrangement.Center,
                                    verticalAlignment = Alignment.Bottom
                                ) {
                                    // Always show the audio levels, but with different styling based on recording state
                                    audioLevels.forEachIndexed { index, level ->
                                        // Animate the height changes for smoother visualization
                                        val targetHeight = if (isRecordingState) {
                                            // Minimum height of 5.dp, then scale up to 60.dp based on level
                                            5 + (level * 55)
                                        } else {
                                            5f // Flat line when not recording
                                        }

                                        val animatedHeight by animateFloatAsState(
                                            targetValue = targetHeight,
                                            animationSpec = tween(durationMillis = 100),
                                            label = "barHeight$index"
                                        )

                                        val color = if (isRecordingState) {
                                            when {
                                                level > 0.7f -> Color.Red
                                                level > 0.4f -> MaterialTheme.colorScheme.primary
                                                else -> MaterialTheme.colorScheme.primary.copy(alpha = 0.7f)
                                            }
                                        } else {
                                            MaterialTheme.colorScheme.surfaceVariant
                                        }

                                        Box(
                                            modifier = Modifier
                                                .padding(horizontal = 3.dp)
                                                .width(10.dp)
                                                .height(animatedHeight.dp)
                                                .background(
                                                    color,
                                                    RoundedCornerShape(
                                                        topStart = 4.dp,
                                                        topEnd = 4.dp
                                                    )
                                                )
                                        )
                                    }
                                }
                            }
                        }

                        // Buttons row
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceEvenly
                        ) {
                            // Cancel button
                            IconButton(
                                onClick = {
                                    stopListening()
                                    showDialog = false
                                    finish()
                                },
                                modifier = Modifier
                                    .size(56.dp)
                                    .clip(CircleShape)
                                    .background(MaterialTheme.colorScheme.errorContainer)
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Close,
                                    contentDescription = stringResource(R.string.cancel),
                                    tint = MaterialTheme.colorScheme.onErrorContainer
                                )
                            }

                            // Record/Stop button
                            IconButton(
                                onClick = {
                                    if (isRecordingState) {
                                        // Stop recording
                                        stopListening()
                                        isRecordingState = false
                                    } else {
                                        // Start recording again
                                        initializeSpeechRecognizer()
                                        isRecordingState = true
                                    }
                                },
                                modifier = Modifier
                                    .size(64.dp)
                                    .clip(CircleShape)
                                    .background(
                                        if (isRecordingState) Color.Red else MaterialTheme.colorScheme.primary
                                    )
                            ) {
                                Icon(
                                    imageVector = if (isRecordingState) Icons.Default.Stop else Icons.Default.Mic,
                                    contentDescription = if (isRecordingState) stringResource(R.string.stop_recording) else stringResource(R.string.start_recording),
                                    tint = Color.White,
                                    modifier = Modifier.size(32.dp)
                                )
                            }

                            // Save button
                            Button(
                                onClick = {
                                    stopListening()
                                    saveTranscription()
                                    showDialog = false
                                },
                                modifier = Modifier
                                    .height(56.dp)
                                    .width(80.dp),
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = MaterialTheme.colorScheme.primaryContainer,
                                    contentColor = MaterialTheme.colorScheme.onPrimaryContainer
                                )
                            ) {
                                Text(stringResource(R.string.save))
                            }
                        }
                    }
                }
            }
        }
    }
}