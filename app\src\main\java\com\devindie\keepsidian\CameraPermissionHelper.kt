package com.devindie.keepsidian

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.pm.PackageManager
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat

/**
 * Helper class for handling camera permissions
 */
object CameraPermissionHelper {
    private const val TAG = "CameraPermissionHelper"

    /**
     * Register permission launcher for camera permission
     * @param activity The activity requesting the permission
     * @param onPermissionGranted Callback to execute when permission is granted
     */
    fun registerPermissionLauncher(
        activity: ComponentActivity,
        onPermissionGranted: () -> Unit
    ): ActivityResultLauncher<String> {
        return activity.registerForActivityResult(
            ActivityResultContracts.RequestPermission()
        ) { isGranted ->
            if (isGranted) {
                Log.d(TAG, "Camera permission granted")
                onPermissionGranted()
            } else {
                Log.d(TAG, "Camera permission denied")
            }
        }
    }

    /**
     * Check if camera permission is granted
     */
    fun hasCameraPermission(context: Context): Boolean {
        return ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.CAMERA
        ) == PackageManager.PERMISSION_GRANTED
    }

    /**
     * Request camera permission if needed
     */
    fun requestCameraPermissionIfNeeded(
        activity: Activity,
        permissionLauncher: ActivityResultLauncher<String>
    ) {
        if (!hasCameraPermission(activity)) {
            permissionLauncher.launch(Manifest.permission.CAMERA)
        }
    }
}
