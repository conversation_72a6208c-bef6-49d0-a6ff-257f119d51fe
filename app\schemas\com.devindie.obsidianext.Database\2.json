{"formatVersion": 1, "database": {"version": 2, "identityHash": "2391326d861f07f678a1e77e4cf4c926", "entities": [{"tableName": "NoteEntity", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER, `title` TEXT NOT NULL, `content` TEXT NOT NULL, `folderId` INTEGER, `isMarkdown` INTEGER NOT NULL, `isDeleted` INTEGER NOT NULL, `timestamp` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": true}, {"fieldPath": "content", "columnName": "content", "affinity": "TEXT", "notNull": true}, {"fieldPath": "folderId", "columnName": "folderId", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "isMarkdown", "columnName": "isMarkdown", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isDeleted", "columnName": "isDeleted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "timestamp", "columnName": "timestamp", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "idx_deleted_timestamp", "unique": false, "columnNames": ["isDeleted", "timestamp"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `idx_deleted_timestamp` ON `${TABLE_NAME}` (`isDeleted`, `timestamp`)"}, {"name": "idx_folder_deleted_timestamp", "unique": false, "columnNames": ["folderId", "isDeleted", "timestamp"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `idx_folder_deleted_timestamp` ON `${TABLE_NAME}` (`folderId`, `isDeleted`, `timestamp`)"}], "foreignKeys": []}, {"tableName": "FolderEntity", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER, `name` TEXT NOT NULL, `color` INTEGER, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "color", "columnName": "color", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '2391326d861f07f678a1e77e4cf4c926')"]}}