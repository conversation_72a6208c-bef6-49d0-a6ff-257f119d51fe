package com.devindie.keepsidian

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.pm.PackageManager
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat

/**
 * Helper class for handling audio recording permissions
 */
object AudioPermissionHelper {
    private const val TAG = "AudioPermissionHelper"

    /**
     * Register permission launcher for audio recording permission
     * @param activity The activity requesting the permission
     * @param onPermissionGranted Callback to execute when permission is granted
     */
    fun registerPermissionLauncher(
        activity: ComponentActivity,
        onPermissionGranted: () -> Unit = {}
    ): ActivityResultLauncher<String> {
        return activity.registerForActivityResult(
            ActivityResultContracts.RequestPermission()
        ) { isGranted ->
            if (isGranted) {
                Log.d(TAG, "Audio recording permission granted")
                onPermissionGranted()
            } else {
                Log.d(TAG, "Audio recording permission denied")
                // No rationale dialog shown as per requirements
            }
        }
    }

    /**
     * Check if audio recording permission is granted
     */
    fun hasAudioPermission(context: Context): Boolean {
        return ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.RECORD_AUDIO
        ) == PackageManager.PERMISSION_GRANTED
    }

    /**
     * Request audio recording permission if needed
     */
    fun requestAudioPermissionIfNeeded(
        activity: Activity,
        permissionLauncher: ActivityResultLauncher<String>
    ) {
        if (!hasAudioPermission(activity)) {
            permissionLauncher.launch(Manifest.permission.RECORD_AUDIO)
        }
    }


}
