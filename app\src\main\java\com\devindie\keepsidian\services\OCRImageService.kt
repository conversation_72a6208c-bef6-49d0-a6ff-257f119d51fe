package com.devindie.keepsidian.services

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Build
import android.util.Log
import androidx.core.net.toUri
import androidx.documentfile.provider.DocumentFile
import com.devindie.keepsidian.AppModule
import com.devindie.keepsidian.Constants
import com.devindie.keepsidian.analytics.AnalyticsHelper
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.text.TextRecognition
import com.google.mlkit.vision.text.TextRecognizer
import com.google.mlkit.vision.text.latin.TextRecognizerOptions
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

class OCRImageService : BaseProcessingService() {
    companion object {
        private const val TAG = "OCRImageService"
        private const val NOTIFICATION_ID = 1002

        const val EXTRA_IMAGE_URI = "com.devindie.keepsidian.extra.IMAGE_URI"
        const val EXTRA_VAULT_URI = "com.devindie.keepsidian.extra.VAULT_URI"
        const val EXTRA_ATTACHMENT_FOLDER = "com.devindie.keepsidian.extra.ATTACHMENT_FOLDER"

        fun startService(context: Context, imageUri: String, vaultUri: String, attachmentFolder: String) {
            val intent = Intent(context, OCRImageService::class.java).apply {
                putExtra(EXTRA_IMAGE_URI, imageUri)
                putExtra(EXTRA_VAULT_URI, vaultUri)
                putExtra(EXTRA_ATTACHMENT_FOLDER, attachmentFolder)
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }
    }

    override val serviceName: String = TAG
    override val notificationId: Int = NOTIFICATION_ID

    private val textRecognizer: TextRecognizer by lazy {
        TextRecognition.getClient(TextRecognizerOptions.DEFAULT_OPTIONS)
    }

    override fun onStartCommand(intent: Intent, flags: Int, startId: Int): Int {
        val startTime = System.currentTimeMillis()

        val imageUriString = intent.getStringExtra(EXTRA_IMAGE_URI) ?: ""
        val vaultUriString = intent.getStringExtra(EXTRA_VAULT_URI) ?: ""
        val attachmentFolderPath = intent.getStringExtra(EXTRA_ATTACHMENT_FOLDER) ?: ""

        if (imageUriString.isEmpty() || vaultUriString.isEmpty()) {
            stopSelf(startId)
            return super.onStartCommand(intent, flags, startId)
        }

        // Start as foreground service
        startForeground(notificationId, createNotification(
            "Processing Image",
            "Performing OCR on image..."
        ))

        // Process in background
        serviceScope.launch {
            try {
                val imageUri = Uri.parse(imageUriString)

                // Step 1: Save the image to the vault's attachment folder
                updateNotification("Processing Image", "Saving image to vault...")
                val savedImageUri = saveImageToVault(imageUri, vaultUriString, attachmentFolderPath)
                if (savedImageUri == null) {
                    updateNotification("Error", "Failed to save image to vault")
                    stopSelf(startId)
                    return@launch
                }

                // Get the relative path of the saved image for embedding in markdown
                val savedImageRelativePath = getRelativePathInVault(savedImageUri, vaultUriString)

                // Step 2: Perform OCR on the image
                updateNotification("Processing Image", "Performing OCR...")
                val bitmap = getBitmapFromUri(imageUri)
                if (bitmap == null) {
                    updateNotification("Error", "Failed to load image")
                    stopSelf(startId)
                    return@launch
                }

                val ocrResult = performOCR(bitmap)

                // Extract title from OCR result
                val title = extractTitleFromOCRResult(ocrResult)

                // Step 3: Create markdown file with the extracted text and embedded image
                updateNotification("Processing Image", "Creating markdown file...")
                val markdownContent = formatAsMarkdown(ocrResult, savedImageRelativePath, title)

                // Step 4: Save the markdown file to the vault
                val fileUri = saveMarkdownToVault(markdownContent, vaultUriString)

                if (fileUri != null) {
                    // Success - update notification
                    updateNotification("OCR Complete", "Image processed and saved to Obsidian")

                    // Add link to today's note
                    addLinkToTodayNote(fileUri, title, vaultUriString, "ocr")
                } else {
                    updateNotification("Error", "Failed to save OCR results")
                }

                val processingTime = System.currentTimeMillis() - startTime
                AnalyticsHelper.trackOCRUsage(true, processingTime)

                // Stop service after a short delay
                Thread.sleep(1000)
                stopSelf(startId)
            } catch (e: Exception) {
                val processingTime = System.currentTimeMillis() - startTime
                AnalyticsHelper.trackOCRUsage(false, processingTime)
                AnalyticsHelper.trackError("ocr_processing", e.message ?: "Unknown error")
                Log.e(TAG, "Error processing image", e)
                updateNotification("Error", "Failed to process image: ${e.message}")
                stopSelf(startId)
            }
        }

        // Use the parent implementation which returns START_REDELIVER_INTENT
        return super.onStartCommand(intent, flags, startId)
    }

    private suspend fun performOCR(bitmap: Bitmap): String = suspendCancellableCoroutine { continuation ->
        try {
            val image = InputImage.fromBitmap(bitmap, 0)

            textRecognizer.process(image)
                .addOnSuccessListener { visionText ->
                    continuation.resume(visionText.text)
                }
                .addOnFailureListener { e ->
                    continuation.resumeWithException(e)
                }
        } catch (e: Exception) {
            continuation.resumeWithException(e)
        }
    }

    // Helper methods for OCR processing
    private fun getBitmapFromUri(uri: Uri): Bitmap? {
        return try {
            contentResolver.openInputStream(uri)?.use { inputStream ->
                BitmapFactory.decodeStream(inputStream)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error loading bitmap from URI", e)
            null
        }
    }

    private fun extractTitleFromOCRResult(ocrText: String): String {
        // Extract a title from the OCR text (first line or first few words)
        val firstLine = ocrText.split("\n").firstOrNull() ?: ""
        return if (firstLine.length > 50) firstLine.take(50) + "..." else firstLine
    }

    private fun formatAsMarkdown(ocrText: String, imagePath: String, title: String): String {
        val sb = StringBuilder()
        val currentDate = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault()).format(Date())

        // Add YAML frontmatter
        sb.append("---\n")
        sb.append("title: $title\n")
        sb.append("date: $currentDate\n")
        sb.append("type: ocr\n")
        sb.append("---\n\n")

        // Add embedded image
        sb.append("![[${imagePath}]]\n\n")

        // Add OCR text
        sb.append("## OCR Text\n\n")
        sb.append(ocrText)

        return sb.toString()
    }

    // Helper methods for OCR processing
    private fun saveImageToVault(imageUri: Uri, vaultUriString: String, attachmentFolderPath: String): Uri? {
        try {
            val vaultUri = vaultUriString.toUri()
            val vaultDoc = DocumentFile.fromTreeUri(this, vaultUri) ?: return null

            // Get the attachment folder path from appDataStore if not provided
            val appDataStore = AppModule.provideAppDataStoreRepository(applicationContext)
            val savedAttachmentFolder = if (attachmentFolderPath.isEmpty()) {
                appDataStore.getStringValue(Constants.Preferences.OBSIDIAN_ATTACHMENT_FOLDER, "")
            } else {
                attachmentFolderPath
            }

            Log.d(TAG, "Original attachment folder path: $savedAttachmentFolder")

            // Navigate to attachment folder or create it if it doesn't exist
            var attachmentDir = vaultDoc

            if (savedAttachmentFolder.isNotEmpty()) {
                // Process the path - if it starts with a dot, it's relative to the vault root
                val processedPath = if (savedAttachmentFolder.startsWith(".")) {
                    // Remove the leading dot and slash if present
                    savedAttachmentFolder.removePrefix("./").removePrefix(".")
                } else {
                    savedAttachmentFolder
                }
                Log.d(TAG, "Processed attachment folder path: $processedPath")

                // Split the path and create each folder
                val folders = processedPath.split("/")
                for (folder in folders) {
                    if (folder.isEmpty()) continue

                    // Find or create each folder in the path
                    val nextFolder = attachmentDir.findFile(folder) ?: attachmentDir.createDirectory(folder)
                    if (nextFolder == null) {
                        Log.e(TAG, "Failed to create folder: $folder")
                        return null
                    }
                    attachmentDir = nextFolder
                }
            } else {
                // Default to "attachments" folder if no specific path is set
                var attachmentsDir = vaultDoc.findFile("attachments")
                if (attachmentsDir == null) {
                    attachmentsDir = vaultDoc.createDirectory("attachments")
                    if (attachmentsDir == null) {
                        Log.e(TAG, "Failed to create attachments folder")
                        return null
                    }
                }
                attachmentDir = attachmentsDir
            }

            // Create a filename based on current date/time
            val timestamp = SimpleDateFormat("yyyyMMdd-HHmmss", Locale.US).format(Date())
            val extension = imageUri.lastPathSegment?.substringAfterLast('.', "jpg") ?: "jpg"
            val fileName = "image-$timestamp.$extension"

            // Log the attachment folder path for debugging
            Log.d(TAG, "Using attachment folder: ${attachmentDir.uri}")

            // Create the file in the attachment folder
            val newFile = attachmentDir.createFile("image/*", fileName) ?: return null

            // Copy the content from the source to the destination
            contentResolver.openInputStream(imageUri)?.use { inputStream ->
                contentResolver.openOutputStream(newFile.uri)?.use { outputStream ->
                    inputStream.copyTo(outputStream)
                }
            }

            return newFile.uri
        } catch (e: Exception) {
            Log.e(TAG, "Error saving image to vault", e)
            return null
        }
    }

    private fun getRelativePathInVault(fileUri: Uri, vaultUriString: String): String {
        try {
            val vaultUri = vaultUriString.toUri()
            val vaultDoc = DocumentFile.fromTreeUri(this, vaultUri) ?: return ""

            // Get the document tree path
            val vaultPath = vaultDoc.uri.path ?: return ""
            val filePath = fileUri.path ?: return ""

            // Extract the relative path by removing the vault path prefix
            var relativePath = filePath.substringAfter(vaultPath)

            // Remove leading slashes and 'document/tree:' parts if present
            relativePath = relativePath.removePrefix("/").removePrefix("document/").removePrefix("tree:")

            // Remove any remaining URI encoding or prefixes
            if (relativePath.contains(":")) {
                relativePath = relativePath.substringAfter(":")
            }

            return relativePath
        } catch (e: Exception) {
            Log.e(TAG, "Error getting relative path", e)
            return ""
        }
    }

    private fun saveMarkdownToVault(content: String, vaultUriString: String): Uri? {
        try {
            val vaultUri = vaultUriString.toUri()
            val vaultDoc = DocumentFile.fromTreeUri(this, vaultUri) ?: return null

            // Create Clippings folder if needed
            var clippingsFolder = vaultDoc.findFile("Clippings")
            if (clippingsFolder == null) {
                clippingsFolder = vaultDoc.createDirectory("Clippings")
                if (clippingsFolder == null) {
                    Log.e(TAG, "Failed to create Clippings folder")
                    return null
                }
            }

            // Create filename with timestamp
            val timestamp = SimpleDateFormat("yyyy-MM-dd-HHmmss", Locale.US).format(Date())
            val fileName = "OCR-$timestamp.md"

            // Create file
            val file = clippingsFolder.createFile("text/markdown", fileName)
            if (file == null) {
                Log.e(TAG, "Failed to create file")
                return null
            }

            // Write content
            contentResolver.openOutputStream(file.uri)?.use { outputStream ->
                outputStream.write(content.toByteArray())
            }

            return file.uri
        } catch (e: Exception) {
            Log.e(TAG, "Error saving markdown to vault", e)
            return null
        }
    }

    private fun addLinkToTodayNote(fileUri: Uri, title: String, vaultUriString: String, fileType: String) {
        // Start the TodayNoteService to add the link
        TodayNoteService.startLinkService(
            context = this,
            fileUri = fileUri.toString(),
            fileTitle = title,
            vaultUri = vaultUriString,
            fileType = fileType
        )
    }
}
